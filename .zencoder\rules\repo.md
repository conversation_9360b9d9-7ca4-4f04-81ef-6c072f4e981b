---
description: Repository Information Overview
alwaysApply: true
---

# نظام إدارة المبيعات (Sales Management System) Information

## Summary
A desktop application for sales management written in Python with a graphical user interface. The application provides features for product management, sales recording, inventory tracking, and data storage in SQLite database. The interface is in Arabic language.

## Structure
Current structure:
- **main.py**: Main application entry point containing the GUI implementation
- **requirements.txt**: Lists Python dependencies
- **test_print.py**: Test script for invoice printing functionality
- **sales.db/sales_management.db**: SQLite database files for data storage
- **فاتورة_مبيعات_*.txt**: Generated sales invoice files in Arabic

Proposed modular structure:
```
project/
│── main.py           # Main file: menu + page connections
│── database.py       # Database connection and creation
│── customers/        
│   └── customers.py  # Customers page
│── suppliers/
│   └── suppliers.py  # Suppliers page
│── inventory/
│   └── inventory.py  # Inventory page
│── invoices/
│   └── invoices.py   # Invoices page
```

## Language & Runtime
**Language**: Python
**Version**: Python 3.13.5
**Build System**: N/A (Python script)
**Package Manager**: pip

## Dependencies
**Main Dependencies**:
- tkinter: GUI toolkit (built-in with Python)
- sqlite3: Database management (built-in with Python)
- matplotlib: Data visualization library

## Build & Installation
```bash
# Install required dependencies
pip install -r requirements.txt

# Run the application
python main.py
```

## Testing
**Framework**: Custom test script
**Test Location**: test_print.py
**Run Command**:
```bash
python test_print.py
```

## Application Features
- **Product Management**: Add and view products with prices and quantities
- **Sales Recording**: Record sales transactions with customer information
- **Inventory Tracking**: Automatic inventory updates based on sales
- **Customer Management**: Track customer information and outstanding debts
- **Supplier Management**: Manage supplier information and payments
- **Dark Mode**: UI theme switching capability
- **Invoice Generation**: Create and print sales invoices in Arabic
- **Data Visualization**: Display sales data in graphical format using matplotlib

## Modularization Plan
The current monolithic application should be refactored into a modular structure:

1. **main.py**: Keep only the main menu and navigation logic
2. **database.py**: Extract all database connection and schema creation code
3. **customers/customers.py**: Move all customer-related functionality
4. **suppliers/suppliers.py**: Move all supplier-related functionality
5. **inventory/inventory.py**: Move all inventory and product management code
6. **invoices/invoices.py**: Move all invoice generation and printing code

This modular approach will improve:
- Code maintainability through separation of concerns
- Readability by reducing file sizes
- Testability of individual components
- Future extensibility of the application