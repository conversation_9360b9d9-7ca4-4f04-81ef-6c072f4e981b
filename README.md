# نظام إدارة المبيعات

نظام شامل لإدارة المبيعات والمخزون والعملاء والموردين باستخدام Python و Tkinter.

## الميزات الرئيسية

### إدارة العملاء والموردين
- إضافة وتعديل وحذف العملاء والموردين
- تتبع ديون العملاء
- بحث متقدم في قاعدة البيانات

### إدارة المخزون والمنتجات
- إدارة شاملة للمنتجات والأسعار
- تتبع الكميات المتوفرة
- تسجيل حركات المخزون

### فاتورة المبيعات المتطورة
- **التاريخ التلقائي**: يتم إدراج التاريخ تلقائياً
- **اختيار العميل**: قائمة منسدلة للعملاء مع خانة بحث
- **إدراج المنتجات**: من المخزون مع خانة بحث وعرض الكمية المتوفرة
- **المجموع الكلي**: حساب تلقائي مع الخصم والضريبة
- **طريقة الدفع**: اختيار بين نقدي أو أجل
- **خصم المخزون**: يتم خصم المنتجات المباعة من المخزون تلقائياً
- **إدارة الديون**: إضافة المبلغ لديون العميل في حالة البيع بالأجل

### إدارة الفواتير
- عرض وإدارة جميع الفواتير
- طباعة الفواتير
- تتبع حالة الدفع

### واجهة المستخدم
- واجهة عربية سهلة الاستخدام
- وضع ليلي ونهاري
- تأثيرات بصرية جذابة
- تنقل سهل بين الأقسام

## متطلبات التشغيل

- Python 3.7 أو أحدث
- tkinter (مدمج مع Python)
- sqlite3 (مدمج مع Python)

## طريقة التشغيل

```bash
python main.py
```

## كيفية استخدام فاتورة المبيعات

1. **اختيار العميل**: استخدم القائمة المنسدلة للعملاء
2. **إضافة المنتجات**: ابحث عن المنتج واختره من القائمة
3. **تحديد الكمية والسعر**: سيتم ملء السعر تلقائياً
4. **اختيار طريقة الدفع**: نقدي أو أجل
5. **حفظ الفاتورة**: سيتم خصم المخزون وإدارة الديون تلقائياً

## هيكل المشروع

- `main.py` - الملف الرئيسي للتطبيق
- `database.py` - إدارة قاعدة البيانات مع دوال متقدمة
- `styles.py` - تنسيقات الواجهة
- `sales_invoice.py` - صفحة فاتورة المبيعات المتطورة
- `customers/` - إدارة العملاء
- `suppliers/` - إدارة الموردين
- `inventory/` - إدارة المخزون
- `invoices/` - إدارة الفواتير

## قاعدة البيانات

يستخدم النظام قاعدة بيانات SQLite مع الجداول التالية:
- `customers` - بيانات العملاء وديونهم
- `suppliers` - بيانات الموردين
- `products` - المنتجات والمخزون
- `invoices` - الفواتير الرئيسية
- `invoice_items` - عناصر الفواتير
- `payments` - المدفوعات
- `stock_movements` - حركات المخزون

## الميزات المتقدمة

### إدارة المخزون التلقائية
- خصم تلقائي للمنتجات المباعة
- تتبع حركات المخزون
- تحديث الكميات في الوقت الفعلي

### إدارة الديون
- تتبع ديون العملاء
- إضافة تلقائية للديون عند البيع بالأجل
- عرض رصيد العميل عند الاختيار

### التحقق من المخزون
- فحص توفر الكمية قبل البيع
- تحذيرات عند نقص المخزون
- منع البيع عند عدم توفر الكمية

## الترخيص

هذا المشروع مفتوح المصدر.