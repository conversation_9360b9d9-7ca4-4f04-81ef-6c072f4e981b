# نظام إدارة المبيعات

نظام شامل لإدارة المبيعات والمخزون والعملاء والموردين باستخدام Python و Tkinter.

## الميزات الرئيسية

### إدارة العملاء والموردين
- إضافة وتعديل وحذف العملاء والموردين
- تتبع ديون العملاء
- بحث متقدم في قاعدة البيانات

### إدارة المخزون والمنتجات
- إدارة شاملة للمنتجات والأسعار
- تتبع الكميات المتوفرة
- تسجيل حركات المخزون

### فاتورة المبيعات المتطورة
- **التاريخ التلقائي**: يتم إدراج التاريخ تلقائياً
- **اختيار العميل**: قائمة منسدلة للعملاء مع خانة بحث
- **إدراج المنتجات**: من المخزون مع خانة بحث وعرض الكمية المتوفرة
- **المجموع الكلي**: حساب تلقائي مع الخصم والضريبة
- **طريقة الدفع**: اختيار بين نقدي أو أجل
- **خصم المخزون**: يتم خصم المنتجات المباعة من المخزون تلقائياً
- **إدارة الديون**: إضافة المبلغ لديون العميل في حالة البيع بالأجل

### إدارة الفواتير
- عرض وإدارة جميع الفواتير
- طباعة الفواتير
- تتبع حالة الدفع

### واجهة المستخدم
- واجهة عربية سهلة الاستخدام
- وضع ليلي ونهاري
- تأثيرات بصرية جذابة
- تنقل سهل بين الأقسام

## متطلبات التشغيل

- Python 3.7 أو أحدث
- tkinter (مدمج مع Python)
- sqlite3 (مدمج مع Python)

## طريقة التشغيل

```bash
python main.py
```

## كيفية استخدام فاتورة المبيعات

1. **اختيار العميل**: استخدم القائمة المنسدلة للعملاء
2. **إضافة المنتجات**: ابحث عن المنتج واختره من القائمة
3. **تحديد الكمية والسعر**: سيتم ملء السعر تلقائياً
4. **اختيار طريقة الدفع**: نقدي أو أجل
5. **حفظ الفاتورة**: سيتم خصم المخزون وإدارة الديون تلقائياً