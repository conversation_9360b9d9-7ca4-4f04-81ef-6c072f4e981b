# نظام إدارة المبيعات

برنامج سطح المكتب لإدارة المبيعات مكتوب بلغة Python مع واجهة مستخدم رسومية.

## المميزات
- إدارة المنتجات (إضافة وعرض)
- تسجيل المبيعات
- تتبع المخزون
- واجهة مستخدم سهلة الاستخدام
- تخزين البيانات في قاعدة بيانات SQLite

## متطلبات التشغيل
- Python 3.x
- Tkinter (مضمن مع Python)
- SQLite3 (مضمن مع Python)

## طريقة التشغيل
1. تأكد من تثبيت Python على جهازك
2. قم بتشغيل البرنامج باستخدام الأمر:
   ```
   python main.py
   ```

## استخدام البرنامج
1. **إدارة المنتجات**:
   - أدخل اسم المنتج والسعر والكمية
   - اضغط على زر "إضافة منتج"
   - سيظهر المنتج في الجدول أدناه

2. **تسجيل المبيعات**:
   - اختر المنتج من القائمة المنسدلة
   - أدخل الكمية المباعة
   - اضغط على زر "تسجيل البيع"
   - سيتم تحديث المخزون تلقائياً