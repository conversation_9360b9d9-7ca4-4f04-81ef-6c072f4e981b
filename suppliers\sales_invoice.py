import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import database

class SalesInvoicePage(tk.Frame):
    def __init__(self, parent, styles, app_instance):
        super().__init__(parent, bg=styles.CARD_COLOR)
        self.styles = styles
        self.app = app_instance
        self.items = []  # قائمة العناصر في الفاتورة
        self.pack(fill=tk.BOTH, expand=True)
        
        self.setup_ui()
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إطار معلومات العميل
        self.create_customer_frame()
        
        # إطار إضافة المنتجات
        self.create_product_frame()
        
        # إطار جدول العناصر
        self.create_items_frame()
        
        # إطار الحسابات والإجماليات
        self.create_totals_frame()
        
        # إطار الأزرار
        self.create_buttons_frame()
        
    def create_customer_frame(self):
        """إطار معلومات العميل"""
        customer_frame = tk.LabelFrame(
            self,
            text="معلومات العميل",
            font=self.styles.FONT_MEDIUM,
            bg=self.app.card_color,
            fg=self.app.text_color,
            relief=tk.GROOVE,
            bd=2
        )
        customer_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # الصف الأول
        row1 = tk.Frame(customer_frame, bg=self.app.card_color)
        row1.pack(fill=tk.X, padx=10, pady=5)
        
        # اسم العميل
        tk.Label(row1, text="اسم العميل:", font=self.styles.FONT_SMALL, 
                bg=self.app.card_color, fg=self.app.text_color).pack(side=tk.RIGHT, padx=5)
        
        self.customer_name = tk.StringVar()
        customer_entry = tk.Entry(row1, textvariable=self.customer_name, font=self.styles.FONT_SMALL, width=25)
        customer_entry.pack(side=tk.RIGHT, padx=5)
        
        # رقم الهاتف
        tk.Label(row1, text="رقم الهاتف:", font=self.styles.FONT_SMALL, 
                bg=self.app.card_color, fg=self.app.text_color).pack(side=tk.LEFT, padx=5)
        
        self.customer_phone = tk.StringVar()
        phone_entry = tk.Entry(row1, textvariable=self.customer_phone, font=self.styles.FONT_SMALL, width=20)
        phone_entry.pack(side=tk.LEFT, padx=5)
        
        # الصف الثاني
        row2 = tk.Frame(customer_frame, bg=self.app.card_color)
        row2.pack(fill=tk.X, padx=10, pady=5)
        
        # رقم الفاتورة
        tk.Label(row2, text="رقم الفاتورة:", font=self.styles.FONT_SMALL, 
                bg=self.app.card_color, fg=self.app.text_color).pack(side=tk.RIGHT, padx=5)
        
        self.invoice_number = tk.StringVar(value=f"INV-{datetime.now().strftime('%Y%m%d-%H%M%S')}")
        invoice_entry = tk.Entry(row2, textvariable=self.invoice_number, font=self.styles.FONT_SMALL, width=25)
        invoice_entry.pack(side=tk.RIGHT, padx=5)
        
        # التاريخ
        tk.Label(row2, text="التاريخ:", font=self.styles.FONT_SMALL, 
                bg=self.app.card_color, fg=self.app.text_color).pack(side=tk.LEFT, padx=5)
        
        self.invoice_date = tk.StringVar(value=datetime.now().strftime('%Y-%m-%d'))
        date_entry = tk.Entry(row2, textvariable=self.invoice_date, font=self.styles.FONT_SMALL, width=20)
        date_entry.pack(side=tk.LEFT, padx=5)
        
    def create_product_frame(self):
        """إطار إضافة المنتجات"""
        product_frame = tk.LabelFrame(
            self,
            text="إضافة منتج",
            font=self.styles.FONT_MEDIUM,
            bg=self.app.card_color,
            fg=self.app.text_color,
            relief=tk.GROOVE,
            bd=2
        )
        product_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # الصف الأول
        row1 = tk.Frame(product_frame, bg=self.app.card_color)
        row1.pack(fill=tk.X, padx=10, pady=5)
        
        # كود المنتج أو البحث
        tk.Label(row1, text="كود/اسم المنتج:", font=self.styles.FONT_SMALL, 
                bg=self.app.card_color, fg=self.app.text_color).pack(side=tk.RIGHT, padx=5)
        
        self.product_search = tk.StringVar()
        product_entry = tk.Entry(row1, textvariable=self.product_search, font=self.styles.FONT_SMALL, width=25)
        product_entry.pack(side=tk.RIGHT, padx=5)
        product_entry.bind('<KeyRelease>', self.search_products)
        
        # قائمة المنتجات
        self.products_listbox = tk.Listbox(row1, font=self.styles.FONT_SMALL, height=3, width=25)
        self.products_listbox.pack(side=tk.RIGHT, padx=5)
        self.products_listbox.bind('<Double-Button-1>', self.select_product)
        
        # الصف الثاني
        row2 = tk.Frame(product_frame, bg=self.app.card_color)
        row2.pack(fill=tk.X, padx=10, pady=5)
        
        # الكمية
        tk.Label(row2, text="الكمية:", font=self.styles.FONT_SMALL, 
                bg=self.app.card_color, fg=self.app.text_color).pack(side=tk.RIGHT, padx=5)
        
        self.quantity = tk.DoubleVar(value=1.0)
        quantity_entry = tk.Entry(row2, textvariable=self.quantity, font=self.styles.FONT_SMALL, width=10)
        quantity_entry.pack(side=tk.RIGHT, padx=5)
        
        # السعر
        tk.Label(row2, text="السعر:", font=self.styles.FONT_SMALL, 
                bg=self.app.card_color, fg=self.app.text_color).pack(side=tk.RIGHT, padx=5)
        
        self.price = tk.DoubleVar(value=0.0)
        price_entry = tk.Entry(row2, textvariable=self.price, font=self.styles.FONT_SMALL, width=10)
        price_entry.pack(side=tk.RIGHT, padx=5)
        
        # زر الإضافة
        add_btn = tk.Button(
            row2, 
            text="إضافة المنتج", 
            font=self.styles.FONT_SMALL,
            bg=self.app.primary_color,
            fg="white",
            command=self.add_item,
            cursor="hand2"
        )
        add_btn.pack(side=tk.LEFT, padx=10)
        
    def create_items_frame(self):
        """إطار جدول العناصر"""
        items_frame = tk.LabelFrame(
            self,
            text="عناصر الفاتورة",
            font=self.styles.FONT_MEDIUM,
            bg=self.app.card_color,
            fg=self.app.text_color,
            relief=tk.GROOVE,
            bd=2
        )
        items_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # إنشاء Treeview
        columns = ('product', 'quantity', 'price', 'total')
        self.items_tree = ttk.Treeview(items_frame, columns=columns, show='headings', height=8)
        
        # تحديد عناوين الأعمدة
        self.items_tree.heading('product', text='المنتج')
        self.items_tree.heading('quantity', text='الكمية')
        self.items_tree.heading('price', text='السعر')
        self.items_tree.heading('total', text='الإجمالي')
        
        # تحديد عرض الأعمدة
        self.items_tree.column('product', width=250, anchor='e')
        self.items_tree.column('quantity', width=80, anchor='center')
        self.items_tree.column('price', width=100, anchor='center')
        self.items_tree.column('total', width=100, anchor='center')
        
        # Scrollbar للجدول
        tree_scrollbar = ttk.Scrollbar(items_frame, orient=tk.VERTICAL, command=self.items_tree.yview)
        self.items_tree.configure(yscrollcommand=tree_scrollbar.set)
        
        # تخطيط الجدول
        self.items_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tree_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # أزرار إدارة العناصر
        buttons_frame = tk.Frame(items_frame, bg=self.app.card_color)
        buttons_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=5)
        
        remove_btn = tk.Button(
            buttons_frame,
            text="حذف العنصر المحدد",
            font=self.styles.FONT_SMALL,
            bg="#dc3545",
            fg="white",
            command=self.remove_item,
            cursor="hand2"
        )
        remove_btn.pack(side=tk.LEFT, padx=5)
        
        clear_btn = tk.Button(
            buttons_frame,
            text="مسح جميع العناصر",
            font=self.styles.FONT_SMALL,
            bg="#ffc107",
            fg="black",
            command=self.clear_items,
            cursor="hand2"
        )
        clear_btn.pack(side=tk.LEFT, padx=5)
        
    def create_totals_frame(self):
        """إطار الحسابات والإجماليات"""
        totals_frame = tk.LabelFrame(
            self,
            text="الحسابات",
            font=self.styles.FONT_MEDIUM,
            bg=self.app.card_color,
            fg=self.app.text_color,
            relief=tk.GROOVE,
            bd=2
        )
        totals_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # الصف الأول - الخصم والضريبة
        row1 = tk.Frame(totals_frame, bg=self.app.card_color)
        row1.pack(fill=tk.X, padx=10, pady=5)
        
        # الخصم
        tk.Label(row1, text="الخصم (%):", font=self.styles.FONT_SMALL, 
                bg=self.app.card_color, fg=self.app.text_color).pack(side=tk.RIGHT, padx=5)
        
        self.discount = tk.DoubleVar(value=0.0)
        discount_entry = tk.Entry(row1, textvariable=self.discount, font=self.styles.FONT_SMALL, width=10)
        discount_entry.pack(side=tk.RIGHT, padx=5)
        discount_entry.bind('<KeyRelease>', lambda e: self.calculate_totals())
        
        # الضريبة
        tk.Label(row1, text="الضريبة (%):", font=self.styles.FONT_SMALL, 
                bg=self.app.card_color, fg=self.app.text_color).pack(side=tk.LEFT, padx=5)
        
        self.tax = tk.DoubleVar(value=15.0)  # ضريبة افتراضية 15%
        tax_entry = tk.Entry(row1, textvariable=self.tax, font=self.styles.FONT_SMALL, width=10)
        tax_entry.pack(side=tk.LEFT, padx=5)
        tax_entry.bind('<KeyRelease>', lambda e: self.calculate_totals())
        
        # الصف الثاني - الإجماليات
        row2 = tk.Frame(totals_frame, bg=self.app.card_color)
        row2.pack(fill=tk.X, padx=10, pady=10)
        
        # المجموع الفرعي
        tk.Label(row2, text="المجموع الفرعي:", font=self.styles.FONT_MEDIUM, 
                bg=self.app.card_color, fg=self.app.text_color).pack(side=tk.RIGHT, padx=5)
        
        self.subtotal_label = tk.Label(row2, text="0.00", font=self.styles.FONT_MEDIUM, 
                                      bg=self.app.card_color, fg=self.app.text_color)
        self.subtotal_label.pack(side=tk.RIGHT, padx=5)
        
        # المجموع النهائي
        tk.Label(row2, text="المجموع النهائي:", font=self.styles.FONT_LARGE, 
                bg=self.app.card_color, fg="green").pack(side=tk.LEFT, padx=5)
        
        self.total_label = tk.Label(row2, text="0.00", font=self.styles.FONT_LARGE, 
                                   bg=self.app.card_color, fg="green")
        self.total_label.pack(side=tk.LEFT, padx=5)
        
    def create_buttons_frame(self):
        """إطار الأزرار"""
        buttons_frame = tk.Frame(self, bg=self.app.card_color)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)
        
        # حفظ الفاتورة
        save_btn = tk.Button(
            buttons_frame,
            text="💾 حفظ الفاتورة",
            font=self.styles.FONT_MEDIUM,
            bg="#28a745",
            fg="white",
            command=self.save_invoice,
            cursor="hand2",
            padx=20,
            pady=10
        )
        save_btn.pack(side=tk.RIGHT, padx=10)
        
        # طباعة الفاتورة
        print_btn = tk.Button(
            buttons_frame,
            text="🖨️ طباعة",
            font=self.styles.FONT_MEDIUM,
            bg="#007bff",
            fg="white",
            command=self.print_invoice,
            cursor="hand2",
            padx=20,
            pady=10
        )
        print_btn.pack(side=tk.RIGHT, padx=5)
        
        # فاتورة جديدة
        new_btn = tk.Button(
            buttons_frame,
            text="📄 فاتورة جديدة",
            font=self.styles.FONT_MEDIUM,
            bg="#ffc107",
            fg="black",
            command=self.new_invoice,
            cursor="hand2",
            padx=20,
            pady=10
        )
        new_btn.pack(side=tk.LEFT, padx=10)
        
    def search_products(self, event=None):
        """البحث عن المنتجات"""
        search_term = self.product_search.get().strip()
        if len(search_term) < 2:
            self.products_listbox.delete(0, tk.END)
            return
            
        try:
            # هنا يجب استدعاء دالة البحث من قاعدة البيانات
            # للتبسيط، سنستخدم قائمة وهمية
            products = [
                {"id": 1, "name": "منتج 1", "price": 50.0, "stock": 10},
                {"id": 2, "name": "منتج 2", "price": 75.0, "stock": 15},
                {"id": 3, "name": "منتج 3", "price": 100.0, "stock": 5},
            ]
            
            # فلترة النتائج
            filtered_products = [p for p in products if search_term.lower() in p["name"].lower()]
            
            self.products_listbox.delete(0, tk.END)
            for product in filtered_products:
                display_text = f"{product['name']} - {product['price']} ر.س - متوفر: {product['stock']}"
                self.products_listbox.insert(tk.END, display_text)
                
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في البحث عن المنتجات: {str(e)}")
            
    def select_product(self, event=None):
        """اختيار منتج من القائمة"""
        selection = self.products_listbox.curselection()
        if selection:
            selected_text = self.products_listbox.get(selection[0])
            # استخراج السعر من النص
            price_part = selected_text.split(" - ")[1].replace(" ر.س", "")
            try:
                self.price.set(float(price_part))
            except ValueError:
                self.price.set(0.0)
                
    def add_item(self):
        """إضافة منتج للفاتورة"""
        product_name = self.product_search.get().strip()
        quantity = self.quantity.get()
        price = self.price.get()
        
        if not product_name:
            messagebox.showwarning("تحذير", "يرجى إدخال اسم المنتج")
            return
            
        if quantity <= 0:
            messagebox.showwarning("تحذير", "يرجى إدخال كمية صحيحة")
            return
            
        if price <= 0:
            messagebox.showwarning("تحذير", "يرجى إدخال سعر صحيح")
            return
            
        # حساب الإجمالي
        total = quantity * price
        
        # إضافة للقائمة والجدول
        item = {
            'product': product_name,
            'quantity': quantity,
            'price': price,
            'total': total
        }
        
        self.items.append(item)
        self.items_tree.insert('', tk.END, values=(product_name, quantity, f"{price:.2f}", f"{total:.2f}"))
        
        # إعادة تعيين الحقول
        self.product_search.set("")
        self.quantity.set(1.0)
        self.price.set(0.0)
        self.products_listbox.delete(0, tk.END)
        
        # إعادة حساب الإجماليات
        self.calculate_totals()
        
    def remove_item(self):
        """حذف عنصر من الفاتورة"""
        selected = self.items_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار عنصر لحذفه")
            return
            
        # الحصول على فهرس العنصر
        item_index = self.items_tree.index(selected[0])
        
        # حذف من القائمة والجدول
        del self.items[item_index]
        self.items_tree.delete(selected[0])
        
        # إعادة حساب الإجماليات
        self.calculate_totals()
        
    def clear_items(self):
        """مسح جميع العناصر"""
        if messagebox.askyesno("تأكيد", "هل تريد مسح جميع العناصر؟"):
            self.items.clear()
            for item in self.items_tree.get_children():
                self.items_tree.delete(item)
            self.calculate_totals()
            
    def calculate_totals(self):
        """حساب الإجماليات"""
        if not self.items:
            self.subtotal_label.config(text="0.00")
            self.total_label.config(text="0.00")
            return
            
        # حساب المجموع الفرعي
        subtotal = sum(item['total'] for item in self.items)
        
        # حساب الخصم
        discount_amount = subtotal * (self.discount.get() / 100)
        after_discount = subtotal - discount_amount
        
        # حساب الضريبة
        tax_amount = after_discount * (self.tax.get() / 100)
        
        # المجموع النهائي
        total = after_discount + tax_amount
        
        # عرض النتائج
        self.subtotal_label.config(text=f"{subtotal:.2f}")
        self.total_label.config(text=f"{total:.2f}")
        
    def save_invoice(self):
        """حفظ الفاتورة"""
        if not self.items:
            messagebox.showwarning("تحذير", "لا توجد عناصر في الفاتورة")
            return
            
        try:
            # هنا يجب إضافة كود حفظ الفاتورة في قاعدة البيانات
            invoice_data = {
                'invoice_number': self.invoice_number.get(),
                'date': self.invoice_date.get(),
                'customer_name': self.customer_name.get(),
                'customer_phone': self.customer_phone.get(),
                'items': self.items,
                'discount': self.discount.get(),
                'tax': self.tax.get(),
                'subtotal': float(self.subtotal_label.cget('text')),
                'total': float(self.total_label.cget('text'))
            }
            
            messagebox.showinfo("نجح", "تم حفظ الفاتورة بنجاح!")
            self.app.update_status("تم حفظ الفاتورة")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ الفاتورة: {str(e)}")
            
    def print_invoice(self):
        """طباعة الفاتورة"""
        if not self.items:
            messagebox.showwarning("تحذير", "لا توجد عناصر للطباعة")
            return
            
        # هنا يمكن إضافة كود الطباعة
        messagebox.showinfo("طباعة", "سيتم إرسال الفاتورة للطابعة...")
        
    def new_invoice(self):
        """بدء فاتورة جديدة"""
        if self.items and not messagebox.askyesno("تأكيد", "هل تريد بدء فاتورة جديدة؟ ستفقد البيانات غير المحفوظة."):
            return
            
        # مسح جميع الحقول
        self.customer_name.set("")
        self.customer_phone.set("")
        self.invoice_number.set(f"INV-{datetime.now().strftime('%Y%m%d-%H%M%S')}")
        self.invoice_date.set(datetime.now().strftime('%Y-%m-%d'))
        self.product_search.set("")
        self.quantity.set(1.0)
        self.price.set(0.0)
        self.discount.set(0.0)
        self.tax.set(15.0)
        
        # مسح العناصر
        self.clear_items()
        
        messagebox.showinfo("جديد", "تم بدء فاتورة جديدة")
