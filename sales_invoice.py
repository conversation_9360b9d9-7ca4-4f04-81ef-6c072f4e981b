import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime
import sqlite3
import database

class SalesInvoicePage(tk.Frame):
    def __init__(self, parent, styles, app_instance):
        super().__init__(parent, bg=styles.CARD_COLOR)
        self.styles = styles
        self.app = app_instance
        self.items = []  # قائمة العناصر في الفاتورة
        self.products_data = []  # بيانات المنتجات المحملة
        self.customers_data = []  # بيانات العملاء المحملة
        self.pack(fill=tk.BOTH, expand=True)
        
        # تحميل البيانات من قاعدة البيانات
        self.load_data()
        self.setup_ui()
        
    def get_connection(self):
        """الحصول على اتصال بقاعدة البيانات"""
        conn = sqlite3.connect('sales_management.db')
        conn.row_factory = sqlite3.Row
        # إنشاء/تحديث الجداول
        self.create_tables(conn)
        return conn
        
    def create_tables(self, conn):
        """إنشاء الجداول المطلوبة"""
        cursor = conn.cursor()
        cursor.executescript('''
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT
            );

            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                price REAL NOT NULL
            );

            CREATE TABLE IF NOT EXISTS invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT UNIQUE NOT NULL,
                customer_id INTEGER,
                date TEXT NOT NULL,
                subtotal REAL NOT NULL DEFAULT 0,
                discount REAL NOT NULL DEFAULT 0,
                tax REAL NOT NULL DEFAULT 0,
                total REAL NOT NULL DEFAULT 0,
                payment_method TEXT NOT NULL DEFAULT 'خالص',
                FOREIGN KEY (customer_id) REFERENCES customers(id)
            );

            CREATE TABLE IF NOT EXISTS invoice_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity REAL NOT NULL,
                unit_price REAL NOT NULL,
                total_price REAL NOT NULL,
                FOREIGN KEY (invoice_id) REFERENCES invoices(id),
                FOREIGN KEY (product_id) REFERENCES products(id)
            );
            
            -- إدراج بيانات تجريبية إذا لم تكن موجودة
            INSERT OR IGNORE INTO customers (id, name, phone) VALUES 
                (1, 'عميل تجريبي 1', '0501234567'),
                (2, 'عميل تجريبي 2', '0559876543'),
                (3, 'عميل نقدي', '0500000000');
                
            INSERT OR IGNORE INTO products (id, name, price) VALUES 
                (1, 'منتج 1', 50.0),
                (2, 'منتج 2', 75.0),
                (3, 'منتج 3', 100.0),
                (4, 'منتج 4', 25.0),
                (5, 'منتج 5', 150.0);
        ''')
        conn.commit()
        
    def load_data(self):
        """تحميل البيانات من قاعدة البيانات الرئيسية"""
        try:
            # تحميل العملاء من قاعدة البيانات الرئيسية
            try:
                self.customers_data = database.get_all_customers()
                # تحويل النتائج إلى قاموس
                self.customers_data = [
                    {'id': row[0], 'name': row[1], 'phone': row[2], 'address': row[3], 'debt_amount': row[4]}
                    for row in self.customers_data
                ]
            except Exception as e:
                print(f"فشل في تحميل العملاء من قاعدة البيانات الرئيسية: {e}")
                # استخدام قاعدة البيانات المحلية كبديل
                with self.get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute('SELECT id, name, phone FROM customers ORDER BY name')
                    self.customers_data = [dict(row) for row in cursor.fetchall()]

            # تحميل المنتجات من قاعدة البيانات الرئيسية
            try:
                self.products_data = database.get_all_products()
                # تحويل النتائج إلى قاموس
                self.products_data = [
                    {'id': row[0], 'name': row[1], 'price': row[2], 'stock_quantity': row[3], 'description': row[4]}
                    for row in self.products_data
                ]
            except Exception as e:
                print(f"فشل في تحميل المنتجات من قاعدة البيانات الرئيسية: {e}")
                # استخدام قاعدة البيانات المحلية كبديل
                with self.get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute('SELECT id, name, price FROM products ORDER BY name')
                    self.products_data = [dict(row) for row in cursor.fetchall()]

        except Exception as e:
            messagebox.showerror("خطأ في قاعدة البيانات", f"فشل في تحميل البيانات: {e}")
            # إنشاء بيانات افتراضية في حالة الفشل
            self.customers_data = []
            self.products_data = []
    
    def generate_invoice_number(self):
        """توليد رقم فاتورة تلقائي"""
        try:
            with self.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('SELECT COUNT(*) FROM invoices')
                count = cursor.fetchone()[0]
                return f"INV-{datetime.now().strftime('%Y%m%d')}-{count + 1:04d}"
        except:
            return f"INV-{datetime.now().strftime('%Y%m%d-%H%M%S')}"
        
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        # إطار معلومات العميل
        self.create_customer_frame()
        
        # إطار إضافة المنتجات
        self.create_product_frame()
        
        # إطار جدول العناصر
        self.create_items_frame()
        
        # إطار الحسابات والإجماليات
        self.create_totals_frame()
        
        # إطار الأزرار
        self.create_buttons_frame()
        
    def create_customer_frame(self):
        """إطار معلومات العميل"""
        customer_frame = tk.LabelFrame(
            self,
            text="معلومات العميل",
            font=self.styles.FONT_MEDIUM,
            bg=self.app.card_color,
            fg=self.app.text_color,
            relief=tk.GROOVE,
            bd=2
        )
        customer_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # الصف الأول
        row1 = tk.Frame(customer_frame, bg=self.app.card_color)
        row1.pack(fill=tk.X, padx=10, pady=5)
        
        # اختيار العميل
        tk.Label(row1, text="اختر العميل:", font=self.styles.FONT_SMALL, 
                bg=self.app.card_color, fg=self.app.text_color).pack(side=tk.RIGHT, padx=5)
        
        # قائمة منسدلة للعملاء
        self.customer_var = tk.StringVar()
        customer_names = [f"{customer['name']} - {customer.get('phone', 'بدون هاتف')}" for customer in self.customers_data]
        self.customer_combo = ttk.Combobox(row1, textvariable=self.customer_var, 
                                          values=customer_names, font=self.styles.FONT_SMALL, width=30, state="readonly")
        self.customer_combo.pack(side=tk.RIGHT, padx=5)
        self.customer_combo.bind('<<ComboboxSelected>>', self.on_customer_select)
        
        # الصف الثاني
        row2 = tk.Frame(customer_frame, bg=self.app.card_color)
        row2.pack(fill=tk.X, padx=10, pady=5)
        
        # رقم الفاتورة (تلقائي)
        tk.Label(row2, text="رقم الفاتورة:", font=self.styles.FONT_SMALL, 
                bg=self.app.card_color, fg=self.app.text_color).pack(side=tk.RIGHT, padx=5)
        
        self.invoice_number = tk.StringVar(value=self.generate_invoice_number())
        invoice_entry = tk.Entry(row2, textvariable=self.invoice_number, font=self.styles.FONT_SMALL, 
                               width=25, state="readonly")
        invoice_entry.pack(side=tk.RIGHT, padx=5)
        
        # التاريخ
        tk.Label(row2, text="التاريخ:", font=self.styles.FONT_SMALL, 
                bg=self.app.card_color, fg=self.app.text_color).pack(side=tk.LEFT, padx=5)
        
        self.invoice_date = tk.StringVar(value=datetime.now().strftime('%Y-%m-%d'))
        date_entry = tk.Entry(row2, textvariable=self.invoice_date, font=self.styles.FONT_SMALL, width=15)
        date_entry.pack(side=tk.LEFT, padx=5)
        
        # طريقة الدفع
        tk.Label(row2, text="طريقة الدفع:", font=self.styles.FONT_SMALL, 
                bg=self.app.card_color, fg=self.app.text_color).pack(side=tk.LEFT, padx=(20, 5))
        
        self.payment_method = tk.StringVar(value="خالص")
        payment_combo = ttk.Combobox(row2, textvariable=self.payment_method, 
                                   values=["خالص", "آجل"], font=self.styles.FONT_SMALL, 
                                   width=10, state="readonly")
        payment_combo.pack(side=tk.LEFT, padx=5)
    
    def on_customer_select(self, event=None):
        """عند اختيار عميل من القائمة"""
        selection = self.customer_combo.current()
        if selection >= 0 and selection < len(self.customers_data):
            customer = self.customers_data[selection]
            # يمكن إضافة معالجة إضافية هنا إذا لزم الأمر
            print(f"تم اختيار العميل: {customer['name']}")
            
    def create_product_frame(self):
        """إطار إضافة المنتجات"""
        product_frame = tk.LabelFrame(
            self,
            text="إضافة منتج",
            font=self.styles.FONT_MEDIUM,
            bg=self.app.card_color,
            fg=self.app.text_color,
            relief=tk.GROOVE,
            bd=2
        )
        product_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # الصف الأول
        row1 = tk.Frame(product_frame, bg=self.app.card_color)
        row1.pack(fill=tk.X, padx=10, pady=5)
        
        # البحث عن المنتج
        tk.Label(row1, text="البحث عن المنتج:", font=self.styles.FONT_SMALL, 
                bg=self.app.card_color, fg=self.app.text_color).pack(side=tk.RIGHT, padx=5)
        
        self.product_search = tk.StringVar()
        product_entry = tk.Entry(row1, textvariable=self.product_search, font=self.styles.FONT_SMALL, width=25)
        product_entry.pack(side=tk.RIGHT, padx=5)
        product_entry.bind('<KeyRelease>', self.search_products)
        
        # قائمة المنتجات
        self.products_listbox = tk.Listbox(row1, font=self.styles.FONT_SMALL, height=4, width=30)
        self.products_listbox.pack(side=tk.RIGHT, padx=5)
        self.products_listbox.bind('<Double-Button-1>', self.select_product)
        self.products_listbox.bind('<Button-1>', self.select_product)
        
        # الصف الثاني
        row2 = tk.Frame(product_frame, bg=self.app.card_color)
        row2.pack(fill=tk.X, padx=10, pady=5)
        
        # الكمية
        tk.Label(row2, text="الكمية:", font=self.styles.FONT_SMALL, 
                bg=self.app.card_color, fg=self.app.text_color).pack(side=tk.RIGHT, padx=5)
        
        self.quantity = tk.DoubleVar(value=1.0)
        quantity_entry = tk.Entry(row2, textvariable=self.quantity, font=self.styles.FONT_SMALL, width=10)
        quantity_entry.pack(side=tk.RIGHT, padx=5)
        
        # السعر
        tk.Label(row2, text="السعر:", font=self.styles.FONT_SMALL, 
                bg=self.app.card_color, fg=self.app.text_color).pack(side=tk.RIGHT, padx=5)
        
        self.price = tk.DoubleVar(value=0.0)
        price_entry = tk.Entry(row2, textvariable=self.price, font=self.styles.FONT_SMALL, width=10)
        price_entry.pack(side=tk.RIGHT, padx=5)
        
        # زر الإضافة
        add_btn = tk.Button(
            row2, 
            text="إضافة المنتج", 
            font=self.styles.FONT_SMALL,
            bg=self.app.primary_color,
            fg="white",
            command=self.add_item,
            cursor="hand2"
        )
        add_btn.pack(side=tk.LEFT, padx=10)
        
        # تحميل جميع المنتجات في البداية
        self.load_all_products()
        
    def load_all_products(self):
        """تحميل جميع المنتجات في القائمة مع عرض الكمية المتوفرة"""
        self.products_listbox.delete(0, tk.END)
        for product in self.products_data:
            stock_info = f" - متوفر: {product.get('stock_quantity', 0)}" if 'stock_quantity' in product else ""
            display_text = f"{product['name']} - {product['price']:.2f} ر.س{stock_info}"
            self.products_listbox.insert(tk.END, display_text)
        
    def search_products(self, event=None):
        """البحث عن المنتجات من قاعدة البيانات"""
        search_term = self.product_search.get().strip().lower()
        self.products_listbox.delete(0, tk.END)

        if not search_term:
            self.load_all_products()
            return

        # فلترة المنتجات
        for product in self.products_data:
            if search_term in product['name'].lower():
                stock_info = f" - متوفر: {product.get('stock_quantity', 0)}" if 'stock_quantity' in product else ""
                display_text = f"{product['name']} - {product['price']:.2f} ر.س{stock_info}"
                self.products_listbox.insert(tk.END, display_text)
                
    def select_product(self, event=None):
        """اختيار منتج من القائمة"""
        selection = self.products_listbox.curselection()
        if selection:
            index = selection[0]
            # العثور على المنتج المطابق
            search_term = self.product_search.get().strip().lower()
            if not search_term:
                # إذا لم يكن هناك بحث، استخدم الفهرس مباشرة
                if index < len(self.products_data):
                    product = self.products_data[index]
                    self.price.set(product['price'])
                    self.product_search.set(product['name'])
            else:
                # إذا كان هناك بحث، ابحث عن المنتج المطابق
                filtered_products = [p for p in self.products_data if search_term in p['name'].lower()]
                if index < len(filtered_products):
                    product = filtered_products[index]
                    self.price.set(product['price'])
                    self.product_search.set(product['name'])

    def create_items_frame(self):
        """إطار جدول العناصر"""
        items_frame = tk.LabelFrame(
            self,
            text="عناصر الفاتورة",
            font=self.styles.FONT_MEDIUM,
            bg=self.app.card_color,
            fg=self.app.text_color,
            relief=tk.GROOVE,
            bd=2
        )
        items_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # إنشاء Treeview
        columns = ('product', 'quantity', 'price', 'total')
        self.items_tree = ttk.Treeview(items_frame, columns=columns, show='headings', height=8)
        
        # تحديد عناوين الأعمدة
        self.items_tree.heading('product', text='المنتج')
        self.items_tree.heading('quantity', text='الكمية')
        self.items_tree.heading('price', text='السعر')
        self.items_tree.heading('total', text='الإجمالي')
        
        # تحديد عرض الأعمدة
        self.items_tree.column('product', width=250, anchor='e')
        self.items_tree.column('quantity', width=80, anchor='center')
        self.items_tree.column('price', width=100, anchor='center')
        self.items_tree.column('total', width=100, anchor='center')
        
        # Scrollbar للجدول
        tree_scrollbar = ttk.Scrollbar(items_frame, orient=tk.VERTICAL, command=self.items_tree.yview)
        self.items_tree.configure(yscrollcommand=tree_scrollbar.set)
        
        # تخطيط الجدول
        self.items_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tree_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # أزرار إدارة العناصر
        buttons_frame = tk.Frame(items_frame, bg=self.app.card_color)
        buttons_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=5)
        
        remove_btn = tk.Button(
            buttons_frame,
            text="حذف العنصر المحدد",
            font=self.styles.FONT_SMALL,
            bg="#dc3545",
            fg="white",
            command=self.remove_item,
            cursor="hand2"
        )
        remove_btn.pack(side=tk.LEFT, padx=5)
        
        clear_btn = tk.Button(
            buttons_frame,
            text="مسح جميع العناصر",
            font=self.styles.FONT_SMALL,
            bg="#ffc107",
            fg="black",
            command=self.clear_items,
            cursor="hand2"
        )
        clear_btn.pack(side=tk.LEFT, padx=5)
        
    def create_totals_frame(self):
        """إطار الحسابات والإجماليات"""
        totals_frame = tk.LabelFrame(
            self,
            text="الحسابات والإجماليات",
            font=self.styles.FONT_MEDIUM,
            bg=self.app.card_color,
            fg=self.app.text_color,
            relief=tk.GROOVE,
            bd=2
        )
        totals_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # الصف الأول - الخصم والضريبة
        row1 = tk.Frame(totals_frame, bg=self.app.card_color)
        row1.pack(fill=tk.X, padx=10, pady=5)
        
        # الخصم
        tk.Label(row1, text="الخصم (%):", font=self.styles.FONT_SMALL, 
                bg=self.app.card_color, fg=self.app.text_color).pack(side=tk.RIGHT, padx=5)
        
        self.discount = tk.DoubleVar(value=0.0)
        discount_entry = tk.Entry(row1, textvariable=self.discount, font=self.styles.FONT_SMALL, width=10)
        discount_entry.pack(side=tk.RIGHT, padx=5)
        discount_entry.bind('<KeyRelease>', lambda e: self.calculate_totals())
        
        # الضريبة
        tk.Label(row1, text="الضريبة (%):", font=self.styles.FONT_SMALL, 
                bg=self.app.card_color, fg=self.app.text_color).pack(side=tk.LEFT, padx=5)
        
        self.tax = tk.DoubleVar(value=15.0)  # ضريبة افتراضية 15%
        tax_entry = tk.Entry(row1, textvariable=self.tax, font=self.styles.FONT_SMALL, width=10)
        tax_entry.pack(side=tk.LEFT, padx=5)
        tax_entry.bind('<KeyRelease>', lambda e: self.calculate_totals())
        
        # الصف الثاني - الإجماليات
        row2 = tk.Frame(totals_frame, bg=self.app.card_color)
        row2.pack(fill=tk.X, padx=10, pady=10)
        
        # المجموع الفرعي
        tk.Label(row2, text="المجموع الفرعي:", font=self.styles.FONT_MEDIUM, 
                bg=self.app.card_color, fg=self.app.text_color).pack(side=tk.RIGHT, padx=5)
        
        self.subtotal_label = tk.Label(row2, text="0.00 ر.س", font=self.styles.FONT_MEDIUM, 
                                      bg=self.app.card_color, fg=self.app.text_color)
        self.subtotal_label.pack(side=tk.RIGHT, padx=5)
        
        # الصف الثالث - مبلغ الخصم والضريبة
        row3 = tk.Frame(totals_frame, bg=self.app.card_color)
        row3.pack(fill=tk.X, padx=10, pady=5)
        
        # مبلغ الخصم
        tk.Label(row3, text="مبلغ الخصم:", font=self.styles.FONT_SMALL, 
                bg=self.app.card_color, fg="red").pack(side=tk.RIGHT, padx=5)
        
        self.discount_amount_label = tk.Label(row3, text="0.00 ر.س", font=self.styles.FONT_SMALL, 
                                            bg=self.app.card_color, fg="red")
        self.discount_amount_label.pack(side=tk.RIGHT, padx=5)
        
        # مبلغ الضريبة
        tk.Label(row3, text="مبلغ الضريبة:", font=self.styles.FONT_SMALL, 
                bg=self.app.card_color, fg="blue").pack(side=tk.LEFT, padx=5)
        
        self.tax_amount_label = tk.Label(row3, text="0.00 ر.س", font=self.styles.FONT_SMALL, 
                                       bg=self.app.card_color, fg="blue")
        self.tax_amount_label.pack(side=tk.LEFT, padx=5)
        
        # الصف الرابع - الإجمالي النهائي
        row4 = tk.Frame(totals_frame, bg=self.app.card_color)
        row4.pack(fill=tk.X, padx=10, pady=(10, 5))
        
        # المجموع النهائي
        tk.Label(row4, text="💰 الإجمالي النهائي:", font=self.styles.FONT_LARGE, 
                bg=self.app.card_color, fg="green").pack(side=tk.RIGHT, padx=5)
        
        self.total_label = tk.Label(row4, text="0.00 ر.س", font=("Arial", 16, "bold"), 
                                   bg=self.app.card_color, fg="green")
        self.total_label.pack(side=tk.RIGHT, padx=5)

    def create_buttons_frame(self):
        """إطار الأزرار"""
        buttons_frame = tk.Frame(self, bg=self.app.card_color)
        buttons_frame.pack(fill=tk.X, padx=10, pady=10)

        # حفظ الفاتورة
        save_btn = tk.Button(
            buttons_frame,
            text="💾 حفظ الفاتورة",
            font=self.styles.FONT_MEDIUM,
            bg="#28a745",
            fg="white",
            command=self.save_invoice,
            cursor="hand2",
            padx=20,
            pady=10
        )
        save_btn.pack(side=tk.RIGHT, padx=10)

        # طباعة الفاتورة
        print_btn = tk.Button(
            buttons_frame,
            text="🖨️ طباعة",
            font=self.styles.FONT_MEDIUM,
            bg="#007bff",
            fg="white",
            command=self.print_invoice,
            cursor="hand2",
            padx=20,
            pady=10
        )
        print_btn.pack(side=tk.RIGHT, padx=5)

        # فاتورة جديدة
        new_btn = tk.Button(
            buttons_frame,
            text="📄 فاتورة جديدة",
            font=self.styles.FONT_MEDIUM,
            bg="#ffc107",
            fg="black",
            command=self.new_invoice,
            cursor="hand2",
            padx=20,
            pady=10
        )
        new_btn.pack(side=tk.LEFT, padx=10)

    def add_item(self):
        """إضافة منتج للفاتورة"""
        product_name = self.product_search.get().strip()
        quantity = self.quantity.get()
        price = self.price.get()

        if not product_name:
            messagebox.showwarning("تحذير", "يرجى اختيار منتج")
            return

        if quantity <= 0:
            messagebox.showwarning("تحذير", "يرجى إدخال كمية صحيحة")
            return

        if price <= 0:
            messagebox.showwarning("تحذير", "يرجى إدخال سعر صحيح")
            return

        # البحث عن معرف المنتج
        product_id = None
        for product in self.products_data:
            if product['name'] == product_name:
                product_id = product['id']
                break

        if not product_id:
            messagebox.showwarning("تحذير", "المنتج غير موجود في قاعدة البيانات")
            return

        # حساب الإجمالي
        total = quantity * price

        # إضافة للقائمة والجدول
        item = {
            'product_id': product_id,
            'product': product_name,
            'quantity': quantity,
            'price': price,
            'total': total
        }

        self.items.append(item)
        self.items_tree.insert('', tk.END, values=(product_name, quantity, f"{price:.2f}", f"{total:.2f}"))

        # إعادة تعيين الحقول
        self.product_search.set("")
        self.quantity.set(1.0)
        self.price.set(0.0)
        self.load_all_products()

        # إعادة حساب الإجماليات
        self.calculate_totals()

    def remove_item(self):
        """حذف عنصر من الفاتورة"""
        selected = self.items_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار عنصر لحذفه")
            return

        # الحصول على فهرس العنصر
        item_index = self.items_tree.index(selected[0])

        # حذف من القائمة والجدول
        del self.items[item_index]
        self.items_tree.delete(selected[0])

        # إعادة حساب الإجماليات
        self.calculate_totals()

    def clear_items(self):
        """مسح جميع العناصر"""
        if messagebox.askyesno("تأكيد", "هل تريد مسح جميع العناصر؟"):
            self.items.clear()
            for item in self.items_tree.get_children():
                self.items_tree.delete(item)
            self.calculate_totals()

    def calculate_totals(self):
        """حساب الإجماليات النهائية"""
        if not self.items:
            self.subtotal_label.config(text="0.00 ر.س")
            self.discount_amount_label.config(text="0.00 ر.س")
            self.tax_amount_label.config(text="0.00 ر.س")
            self.total_label.config(text="0.00 ر.س")
            return

        # حساب المجموع الفرعي
        subtotal = sum(item['total'] for item in self.items)

        # حساب مبلغ الخصم
        discount_percentage = self.discount.get() if self.discount.get() >= 0 else 0
        discount_amount = subtotal * (discount_percentage / 100)
        after_discount = subtotal - discount_amount

        # حساب مبلغ الضريبة
        tax_percentage = self.tax.get() if self.tax.get() >= 0 else 0
        tax_amount = after_discount * (tax_percentage / 100)

        # المجموع النهائي
        final_total = after_discount + tax_amount

        # عرض النتائج
        self.subtotal_label.config(text=f"{subtotal:.2f} ر.س")
        self.discount_amount_label.config(text=f"{discount_amount:.2f} ر.س")
        self.tax_amount_label.config(text=f"{tax_amount:.2f} ر.س")
        self.total_label.config(text=f"{final_total:.2f} ر.س")

    def save_invoice(self):
        """حفظ الفاتورة مع خصم المخزون وإدارة الديون"""
        if not self.items:
            messagebox.showwarning("تحذير", "لا توجد عناصر في الفاتورة")
            return

        customer_selection = self.customer_combo.current()
        if customer_selection < 0:
            messagebox.showwarning("تحذير", "يرجى اختيار عميل")
            return

        try:
            customer = self.customers_data[customer_selection]

            # التحقق من توفر المنتجات في المخزون
            insufficient_stock = []
            for item in self.items:
                # البحث عن المنتج في قاعدة البيانات الرئيسية
                try:
                    with database.get_connection() as main_conn:
                        cursor = main_conn.cursor()
                        cursor.execute("SELECT stock_quantity FROM products WHERE name = ?", (item['product'],))
                        result = cursor.fetchone()
                        if result:
                            available_stock = result[0]
                            if available_stock < item['quantity']:
                                insufficient_stock.append(f"{item['product']}: متوفر {available_stock}, مطلوب {item['quantity']}")
                except:
                    # إذا فشل الاتصال بقاعدة البيانات الرئيسية، استخدم القاعدة المحلية
                    pass

            if insufficient_stock:
                messagebox.showerror("خطأ في المخزون",
                    "المنتجات التالية غير متوفرة بالكمية المطلوبة:\n" + "\n".join(insufficient_stock))
                return

            with self.get_connection() as conn:
                cursor = conn.cursor()

                # حساب الإجماليات النهائية
                subtotal = sum(item['quantity'] * item['price'] for item in self.items)
                discount_percentage = self.discount.get() if self.discount.get() >= 0 else 0
                discount_amount = subtotal * (discount_percentage / 100)
                after_discount = subtotal - discount_amount

                tax_percentage = self.tax.get() if self.tax.get() >= 0 else 0
                tax_amount = after_discount * (tax_percentage / 100)

                final_total = after_discount + tax_amount

                # حفظ الفاتورة مع طريقة الدفع
                cursor.execute('''
                    INSERT INTO invoices (invoice_number, customer_id, date, subtotal, discount, tax, total, payment_method)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ''', (self.invoice_number.get(), customer['id'], self.invoice_date.get(),
                     subtotal, discount_percentage, tax_percentage, final_total, self.payment_method.get()))

                invoice_id = cursor.lastrowid

                # حفظ عناصر الفاتورة وخصم المخزون
                for item in self.items:
                    # حفظ عنصر الفاتورة
                    cursor.execute('''
                        INSERT INTO invoice_items (invoice_id, product_id, quantity, unit_price, total_price)
                        VALUES (?, ?, ?, ?, ?)
                    ''', (invoice_id, item['product_id'], item['quantity'], item['price'], item['total']))

                    # خصم الكمية من المخزون في قاعدة البيانات الرئيسية
                    try:
                        with database.get_connection() as main_conn:
                            main_cursor = main_conn.cursor()
                            main_cursor.execute("""
                                UPDATE products
                                SET stock_quantity = stock_quantity - ?,
                                    quantity = quantity - ?,
                                    updated_date = ?
                                WHERE name = ?
                            """, (item['quantity'], item['quantity'], datetime.now().isoformat(), item['product']))

                            # تسجيل حركة المخزون
                            main_cursor.execute("""
                                INSERT INTO stock_movements (
                                    product_id, movement_type, quantity, reference_type,
                                    reference_id, notes, movement_date, created_date
                                )
                                SELECT id, 'out', ?, 'sales_invoice', ?, ?, ?, ?
                                FROM products WHERE name = ?
                            """, (
                                item['quantity'], invoice_id,
                                f"بيع - فاتورة رقم {self.invoice_number.get()}",
                                self.invoice_date.get(), datetime.now().isoformat(),
                                item['product']
                            ))
                            main_conn.commit()
                    except Exception as e:
                        print(f"تحذير: فشل في تحديث المخزون للمنتج {item['product']}: {e}")

                # إدارة الديون حسب طريقة الدفع
                if self.payment_method.get() == "آجل":
                    # إضافة المبلغ لديون العميل في قاعدة البيانات الرئيسية
                    try:
                        with database.get_connection() as main_conn:
                            main_cursor = main_conn.cursor()
                            main_cursor.execute("""
                                UPDATE customers
                                SET debt_amount = debt_amount + ?,
                                    updated_date = ?
                                WHERE name = ? OR phone = ?
                            """, (final_total, datetime.now().isoformat(), customer['name'], customer.get('phone', '')))
                            main_conn.commit()
                    except Exception as e:
                        print(f"تحذير: فشل في تحديث ديون العميل: {e}")

                conn.commit()

            payment_status = "نقدي" if self.payment_method.get() == "خالص" else "آجل"
            debt_message = f"\nتم إضافة {final_total:.2f} ر.س لديون العميل" if self.payment_method.get() == "آجل" else ""

            messagebox.showinfo("نجح",
                f"تم حفظ الفاتورة رقم {self.invoice_number.get()} بنجاح!\n"
                f"الإجمالي: {final_total:.2f} ر.س\n"
                f"طريقة الدفع: {payment_status}{debt_message}\n"
                f"تم خصم المنتجات من المخزون")

            self.app.update_status(f"تم حفظ الفاتورة - الإجمالي: {final_total:.2f} ر.س")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ الفاتورة: {str(e)}")

    def print_invoice(self):
        """طباعة الفاتورة مع الإجماليات"""
        if not self.items:
            messagebox.showwarning("تحذير", "لا توجد عناصر للطباعة")
            return

        # حساب الإجماليات للطباعة
        subtotal = sum(item['total'] for item in self.items)
        discount_amount = subtotal * (self.discount.get() / 100)
        tax_amount = (subtotal - discount_amount) * (self.tax.get() / 100)
        final_total = subtotal - discount_amount + tax_amount
        
        print_text = f"""
        ====== فاتورة مبيعات ======
        رقم الفاتورة: {self.invoice_number.get()}
        التاريخ: {self.invoice_date.get()}
        العميل: {self.customer_var.get()}
        طريقة الدفع: {self.payment_method.get()}
        
        === العناصر ===
        """
        
        for item in self.items:
            print_text += f"{item['product']} | {item['quantity']} × {item['price']:.2f} = {item['total']:.2f} ر.س\n"
            
        print_text += f"""
        === الحسابات ===
        المجموع الفرعي: {subtotal:.2f} ر.س
        الخصم ({self.discount.get()}%): -{discount_amount:.2f} ر.س
        الضريبة ({self.tax.get()}%): +{tax_amount:.2f} ر.س
        
        === الإجمالي النهائي ===
        {final_total:.2f} ر.س
        ========================
        """
        
        print(print_text)
        messagebox.showinfo("طباعة", "تم إرسال الفاتورة للطابعة...")

    def new_invoice(self):
        """بدء فاتورة جديدة"""
        if self.items and not messagebox.askyesno("تأكيد", "هل تريد بدء فاتورة جديدة؟ ستفقد البيانات غير المحفوظة."):
            return

        # مسح جميع الحقول
        self.customer_combo.set("")
        self.invoice_number.set(self.generate_invoice_number())
        self.invoice_date.set(datetime.now().strftime('%Y-%m-%d'))
        self.payment_method.set("خالص")
        self.product_search.set("")
        self.quantity.set(1.0)
        self.price.set(0.0)
        self.discount.set(0.0)
        self.tax.set(15.0)

        # مسح العناصر
        self.clear_items()
        
        # إعادة تحميل المنتجات
        self.load_all_products()

        messagebox.showinfo("جديد", "تم بدء فاتورة جديدة")
