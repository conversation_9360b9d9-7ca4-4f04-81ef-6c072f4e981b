import tkinter as tk
from tkinter import ttk, messagebox
import database
from datetime import datetime

class CustomersPage:
    def __init__(self, parent):
        self.parent = parent
        self.frame = tk.Frame(parent, bg="#f0f0f0")
        self.frame.pack(fill="both", expand=True)
        
        # ==================== رأس الصفحة المحسن ====================
        header_frame = tk.Frame(self.frame, bg="#2c3e50", height=70)
        header_frame.pack(fill="x")
        header_frame.pack_propagate(False)
        
        # إطار العنوان والإحصائيات
        main_header_frame = tk.Frame(header_frame, bg="#2c3e50")
        main_header_frame.pack(fill="x", padx=20, pady=10)
        
        # العنوان الرئيسي على اليسار
        header_label = tk.Label(
            main_header_frame, 
            text="إدارة العملاء", 
            font=("Arial", 16, "bold"),
            bg="#2c3e50",
            fg="white"
        )
        header_label.pack(side="left")
        
        # إطار الإحصائيات على اليمين
        stats_frame = tk.Frame(main_header_frame, bg="#2c3e50")
        stats_frame.pack(side="right")
        
        # بطاقات صغيرة الحجم
        # بطاقة عدد العملاء
        customers_card = tk.Frame(stats_frame, bg="#3498db", relief="flat", bd=1)
        customers_card.pack(side="left", padx=5)
        
        tk.Label(
            customers_card,
            text="👥",
            font=("Arial", 8),
            bg="#3498db",
            fg="white",
            padx=3,
            pady=2
        ).pack(side="left")
        
        self.header_customers_count = tk.Label(
            customers_card,
            text="0 عميل",
            font=("Arial", 9, "bold"),
            bg="#3498db",
            fg="white",
            padx=5,
            pady=2
        )
        self.header_customers_count.pack(side="left")
        
        # بطاقة إجمالي الديون
        debt_card = tk.Frame(stats_frame, bg="#e74c3c", relief="flat", bd=1)
        debt_card.pack(side="left", padx=5)
        
        tk.Label(
            debt_card,
            text="💰",
            font=("Arial", 8),
            bg="#e74c3c",
            fg="white",
            padx=3,
            pady=2
        ).pack(side="left")
        
        self.header_total_debt = tk.Label(
            debt_card,
            text="0.00 ج.م",
            font=("Arial", 9, "bold"),
            bg="#e74c3c",
            fg="white",
            padx=5,
            pady=2
        )
        self.header_total_debt.pack(side="left")
        
        # بطاقة العملاء المدينين
        debtors_card = tk.Frame(stats_frame, bg="#f39c12", relief="flat", bd=1)
        debtors_card.pack(side="left", padx=5)
        
        tk.Label(
            debtors_card,
            text="⚠️",
            font=("Arial", 8),
            bg="#f39c12",
            fg="white",
            padx=3,
            pady=2
        ).pack(side="left")
        
        self.header_debtors_count = tk.Label(
            debtors_card,
            text="0 مدين",
            font=("Arial", 9, "bold"),
            bg="#f39c12",
            fg="white",
            padx=5,
            pady=2
        )
        self.header_debtors_count.pack(side="left")
        
        # ==================== شريط الأدوات ====================
        toolbar_frame = tk.Frame(self.frame, bg="#ecf0f1", height=50)
        toolbar_frame.pack(fill="x", pady=(0, 10))
        toolbar_frame.pack_propagate(False)
        
        # الأزرار مع تحسين التصميم
        btn_style = {
            "font": ("Arial", 11, "bold"),
            "width": 12,
            "height": 2,
            "cursor": "hand2",
            "relief": "raised",
            "bd": 2
        }
        
        self.add_btn = tk.Button(
            toolbar_frame, 
            text="➕ إضافة عميل",
            command=self.show_add_dialog,
            bg="#27ae60",
            fg="white",
            **btn_style
        )
        self.add_btn.pack(side="right", padx=10, pady=5)
        
        self.edit_btn = tk.Button(
            toolbar_frame,
            text="✏️ تعديل",
            command=self.show_edit_dialog,
            bg="#3498db",
            fg="white",
            **btn_style
        )
        self.edit_btn.pack(side="right", padx=5, pady=5)
        
        self.delete_btn = tk.Button(
            toolbar_frame,
            text="🗑️ حذف",
            command=self.delete_customer,
            bg="#e74c3c",
            fg="white",
            **btn_style
        )
        self.delete_btn.pack(side="right", padx=5, pady=5)
        
        self.refresh_btn = tk.Button(
            toolbar_frame,
            text="🔄 تحديث",
            command=self.load_customers,
            bg="#95a5a6",
            fg="white",
            **btn_style
        )
        self.refresh_btn.pack(side="right", padx=5, pady=5)
        
        # ==================== شريط البحث ====================
        search_frame = tk.Frame(toolbar_frame, bg="#ecf0f1")
        search_frame.pack(side="left", padx=20, pady=5)
        
        tk.Label(
            search_frame, 
            text="🔍 بحث:",
            font=("Arial", 11),
            bg="#ecf0f1"
        ).pack(side="left", padx=5)
        
        self.search_entry = tk.Entry(search_frame, width=25, font=("Arial", 11))
        self.search_entry.pack(side="left")
        self.search_entry.bind("<KeyRelease>", self.search_customers)
        
        # ==================== الجدول ====================
        table_frame = tk.Frame(self.frame, bg="white")
        table_frame.pack(fill="both", expand=True, padx=10, pady=5)
        
        # تحسين ستايل الجدول
        style = ttk.Style()
        style.theme_use("clam")
        style.configure(
            "Treeview",
            background="white",
            fieldbackground="white",
            foreground="black",
            font=("Arial", 10),
            rowheight=30
        )
        style.configure(
            "Treeview.Heading",
            background="#34495e",
            foreground="white",
            font=("Arial", 11, "bold"),
            relief="flat"
        )
        style.map("Treeview", background=[("selected", "#3498db")])
        
        # إنشاء الجدول مع scrollbars
        tree_scroll_y = tk.Scrollbar(table_frame, orient="vertical")
        tree_scroll_x = tk.Scrollbar(table_frame, orient="horizontal")
        
        self.tree = ttk.Treeview(
            table_frame,
            columns=("id", "name", "phone", "address", "debt_amount", "notes", "created_date"),
            show="headings",
            yscrollcommand=tree_scroll_y.set,
            xscrollcommand=tree_scroll_x.set
        )
        
        tree_scroll_y.config(command=self.tree.yview)
        tree_scroll_x.config(command=self.tree.xview)
        tree_scroll_y.pack(side="right", fill="y")
        tree_scroll_x.pack(side="bottom", fill="x")
        self.tree.pack(fill="both", expand=True)
        
        # تكوين الأعمدة
        columns_config = [
            ("id", "الرقم", 50),
            ("name", "اسم العميل", 150),
            ("phone", "رقم الهاتف", 120),
            ("address", "العنوان", 200),
            ("debt_amount", "المديونية", 100),
            ("notes", "ملاحظات", 200),
            ("created_date", "تاريخ التسجيل", 120)
        ]
        
        for col, heading, width in columns_config:
            self.tree.heading(col, text=heading)
            self.tree.column(col, width=width, anchor="center" if col in ["id", "debt_amount"] else "w")
        
        # إضافة الألوان للصفوف
        self.tree.tag_configure("oddrow", background="#f8f9fa")
        self.tree.tag_configure("evenrow", background="#ffffff")
        self.tree.tag_configure("debt", foreground="#e74c3c", font=("Arial", 10, "bold"))
        
        # ==================== شريط الحالة ====================
        status_frame = tk.Frame(self.frame, bg="#2c3e50", height=30)
        status_frame.pack(fill="x", side="bottom")
        status_frame.pack_propagate(False)
        
        self.status_label = tk.Label(
            status_frame,
            text="جاهز",
            font=("Arial", 10),
            bg="#2c3e50",
            fg="white"
        )
        self.status_label.pack(side="left", padx=10, pady=5)
        
        self.count_label = tk.Label(
            status_frame,
            text="إجمالي العملاء: 0",
            font=("Arial", 10),
            bg="#2c3e50",
            fg="white"
        )
        self.count_label.pack(side="right", padx=10, pady=5)
        
        # تحميل البيانات
        self.load_customers()
    
    # ==================== تحديث إحصائيات الرأس ====================
    def update_header_stats(self, total_customers, total_debt, debtors_count):
        """تحديث الإحصائيات في رأس الصفحة"""
        self.header_customers_count.config(text=f"{total_customers} عميل")
        self.header_total_debt.config(text=f"{total_debt:,.0f} ج.م")
        self.header_debtors_count.config(text=f"{debtors_count} مدين")
    
    # ==================== نافذة إضافة عميل ====================
    def show_add_dialog(self):
        dialog = tk.Toplevel(self.parent)
        dialog.title("إضافة عميل جديد")
        dialog.geometry("500x400+400+200")
        dialog.configure(bg="#ecf0f1")
        dialog.resizable(False, False)
        dialog.grab_set()  # جعل النافذة modal
        
        # رأس النافذة
        header = tk.Label(
            dialog,
            text="إضافة عميل جديد",
            font=("Arial", 16, "bold"),
            bg="#3498db",
            fg="white",
            pady=10
        )
        header.pack(fill="x")
        
        # إطار الحقول
        fields_frame = tk.Frame(dialog, bg="#ecf0f1", padx=30, pady=20)
        fields_frame.pack(fill="both", expand=True)
        
        # الحقول
        fields = []
        labels = [
            ("اسم العميل *", "name"),
            ("رقم الهاتف *", "phone"),
            ("العنوان", "address"),
            ("المديونية", "debt"),
            ("ملاحظات", "notes")
        ]
        
        entries = {}
        for i, (label_text, field_name) in enumerate(labels):
            label = tk.Label(
                fields_frame,
                text=label_text,
                font=("Arial", 11),
                bg="#ecf0f1",
                anchor="w"
            )
            label.grid(row=i, column=0, sticky="w", pady=10, padx=(0, 10))
            
            if field_name == "notes":
                entry = tk.Text(fields_frame, width=35, height=3, font=("Arial", 10))
            else:
                entry = tk.Entry(fields_frame, width=35, font=("Arial", 10))
            entry.grid(row=i, column=1, pady=10)
            entries[field_name] = entry
        
        # أزرار الإجراءات
        btn_frame = tk.Frame(dialog, bg="#ecf0f1", pady=10)
        btn_frame.pack(fill="x")
        
        def save_customer():
            name = entries["name"].get().strip()
            phone = entries["phone"].get().strip()
            address = entries["address"].get().strip()
            debt_text = entries["debt"].get().strip()
            debt = float(debt_text) if debt_text else 0.0
            notes = entries["notes"].get("1.0", "end-1c").strip() if isinstance(entries["notes"], tk.Text) else entries["notes"].get().strip()
            
            if not name or not phone:
                messagebox.showerror("خطأ", "اسم العميل ورقم الهاتف مطلوبان", parent=dialog)
                return
            
            try:
                conn = database.get_connection()
                cursor = conn.cursor()
                cursor.execute(
                    """
                    INSERT INTO customers (name, phone, address, notes, debt_amount, created_date, updated_date)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                    """,
                    (name, phone, address, notes, debt, datetime.now().isoformat(), datetime.now().isoformat())
                )
                conn.commit()
                conn.close()
                
                self.load_customers()
                self.update_status(f"تم إضافة العميل: {name}")
                dialog.destroy()
                messagebox.showinfo("نجاح", "تم إضافة العميل بنجاح")
                
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}", parent=dialog)
        
        save_btn = tk.Button(
            btn_frame,
            text="💾 حفظ",
            command=save_customer,
            bg="#27ae60",
            fg="white",
            font=("Arial", 11, "bold"),
            width=10,
            cursor="hand2"
        )
        save_btn.pack(side="right", padx=10)
        
        cancel_btn = tk.Button(
            btn_frame,
            text="❌ إلغاء",
            command=dialog.destroy,
            bg="#e74c3c",
            fg="white",
            font=("Arial", 11, "bold"),
            width=10,
            cursor="hand2"
        )
        cancel_btn.pack(side="right")
        
        # التركيز على أول حقل
        entries["name"].focus()
    
    # ==================== نافذة تعديل عميل ====================
    def show_edit_dialog(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("تنبيه", "الرجاء اختيار عميل للتعديل")
            return
        
        item = self.tree.item(selected)
        values = item["values"]
        
        dialog = tk.Toplevel(self.parent)
        dialog.title("تعديل بيانات العميل")
        dialog.geometry("500x400+400+200")
        dialog.configure(bg="#ecf0f1")
        dialog.resizable(False, False)
        dialog.grab_set()
        
        # رأس النافذة
        header = tk.Label(
            dialog,
            text="تعديل بيانات العميل",
            font=("Arial", 16, "bold"),
            bg="#3498db",
            fg="white",
            pady=10
        )
        header.pack(fill="x")
        
        # إطار الحقول
        fields_frame = tk.Frame(dialog, bg="#ecf0f1", padx=30, pady=20)
        fields_frame.pack(fill="both", expand=True)
        
        # الحقول مع القيم الحالية
        entries = {}
        field_values = {
            "name": values[1],
            "phone": values[2],
            "address": values[3] or "",
            "debt": str(values[4].replace(" ج.م", "").replace(",", "")) if values[4] else "0",
            "notes": values[5] or ""
        }
        
        labels = [
            ("اسم العميل *", "name"),
            ("رقم الهاتف *", "phone"),
            ("العنوان", "address"),
            ("المديونية", "debt"),
            ("ملاحظات", "notes")
        ]
        
        for i, (label_text, field_name) in enumerate(labels):
            label = tk.Label(
                fields_frame,
                text=label_text,
                font=("Arial", 11),
                bg="#ecf0f1",
                anchor="w"
            )
            label.grid(row=i, column=0, sticky="w", pady=10, padx=(0, 10))
            
            if field_name == "notes":
                entry = tk.Text(fields_frame, width=35, height=3, font=("Arial", 10))
                entry.insert("1.0", field_values[field_name])
            else:
                entry = tk.Entry(fields_frame, width=35, font=("Arial", 10))
                entry.insert(0, field_values[field_name])
            entry.grid(row=i, column=1, pady=10)
            entries[field_name] = entry
        
        # أزرار الإجراءات
        btn_frame = tk.Frame(dialog, bg="#ecf0f1", pady=10)
        btn_frame.pack(fill="x")
        
        def update_customer():
            name = entries["name"].get().strip()
            phone = entries["phone"].get().strip()
            address = entries["address"].get().strip()
            debt_text = entries["debt"].get().strip()
            debt = float(debt_text) if debt_text else 0.0
            notes = entries["notes"].get("1.0", "end-1c").strip() if isinstance(entries["notes"], tk.Text) else entries["notes"].get().strip()
            
            if not name or not phone:
                messagebox.showerror("خطأ", "اسم العميل ورقم الهاتف مطلوبان", parent=dialog)
                return
            
            try:
                conn = database.get_connection()
                cursor = conn.cursor()
                cursor.execute(
                    """
                    UPDATE customers
                    SET name=?, phone=?, address=?, debt_amount=?, notes=?, updated_date=?
                    WHERE id=?
                    """,
                    (name, phone, address, debt, notes, datetime.now().isoformat(), values[0])
                )
                conn.commit()
                conn.close()
                
                self.load_customers()
                self.update_status(f"تم تحديث بيانات العميل: {name}")
                dialog.destroy()
                messagebox.showinfo("نجاح", "تم تحديث البيانات بنجاح")
                
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء التحديث: {str(e)}", parent=dialog)
        
        update_btn = tk.Button(
            btn_frame,
            text="💾 تحديث",
            command=update_customer,
            bg="#27ae60",
            fg="white",
            font=("Arial", 11, "bold"),
            width=10,
            cursor="hand2"
        )
        update_btn.pack(side="right", padx=10)
        
        cancel_btn = tk.Button(
            btn_frame,
            text="❌ إلغاء",
            command=dialog.destroy,
            bg="#e74c3c",
            fg="white",
            font=("Arial", 11, "bold"),
            width=10,
            cursor="hand2"
        )
        cancel_btn.pack(side="right")
    
    # ==================== تحميل البيانات ====================
    def load_customers(self):
        for i in self.tree.get_children():
            self.tree.delete(i)
        
        try:
            conn = database.get_connection()
            cursor = conn.cursor()
            cursor.execute(
                """
                SELECT id, name, phone, address, debt_amount, notes, created_date 
                FROM customers 
                ORDER BY id DESC
                """
            )
            rows = cursor.fetchall()
            conn.close()
            
            total_debt = 0.0  # متغير لحساب مجموع الديون
            debtors_count = 0  # متغير لعدد العملاء المدينين
            
            for i, row in enumerate(rows):
                # تنسيق التاريخ
                date_str = row[6]
                try:
                    date_obj = datetime.fromisoformat(date_str)
                    formatted_date = date_obj.strftime("%Y-%m-%d")
                except:
                    formatted_date = date_str[:10] if date_str else ""
                
                # تنسيق المديونية
                debt = row[4] or 0
                total_debt += debt  # إضافة الدين إلى المجموع
                if debt > 0:
                    debtors_count += 1  # عدد العملاء المدينين
                
                formatted_row = list(row[:6]) + [formatted_date]
                formatted_row[4] = f"{debt:,.2f} ج.م" if debt > 0 else "0.00 ج.م"
                
                # إضافة الصف مع التنسيق
                tags = ("evenrow" if i % 2 == 0 else "oddrow",)
                if debt > 0:
                    tags = tags + ("debt",)
                
                self.tree.insert("", "end", values=formatted_row, tags=tags)
            
            # تحديث عداد العملاء ومجموع الديون
            self.count_label.config(text=f"إجمالي العملاء: {len(rows)}")
            
            # تحديث إحصائيات الرأس
            self.update_header_stats(len(rows), total_debt, debtors_count)
            
            self.update_status(f"تم تحميل {len(rows)} عميل")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل تحميل البيانات: {str(e)}")
    
    # ==================== حذف عميل ====================
    def delete_customer(self):
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("تنبيه", "الرجاء اختيار عميل للحذف")
            return
        
        item = self.tree.item(selected)
        customer_name = item["values"][1]
        customer_id = item["values"][0]
        
        result = messagebox.askyesno(
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف العميل:\n{customer_name}؟",
            icon="warning"
        )
        
        if result:
            try:
                conn = database.get_connection()
                cursor = conn.cursor()
                cursor.execute("DELETE FROM customers WHERE id=?", (customer_id,))
                conn.commit()
                conn.close()
                
                self.load_customers()
                self.update_status(f"تم حذف العميل: {customer_name}")
                messagebox.showinfo("نجاح", "تم حذف العميل بنجاح")
                
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل حذف العميل: {str(e)}")
    
    # ==================== البحث ====================
    def search_customers(self, event=None):
        search_term = self.search_entry.get().strip().lower()
        
        for item in self.tree.get_children():
            self.tree.delete(item)
        
        try:
            conn = database.get_connection()
            cursor = conn.cursor()
            
            if search_term:
                cursor.execute(
                    """
                    SELECT id, name, phone, address, debt_amount, notes, created_date
                    FROM customers
                    WHERE LOWER(name) LIKE ? OR LOWER(phone) LIKE ? OR LOWER(address) LIKE ?
                    ORDER BY id DESC
                    """,
                    (f"%{search_term}%", f"%{search_term}%", f"%{search_term}%")
                )
            else:
                cursor.execute(
                    """
                    SELECT id, name, phone, address, debt_amount, notes, created_date
                    FROM customers
                    ORDER BY id DESC
                    """
                )
            
            rows = cursor.fetchall()
            conn.close()
            
            total_debt = 0.0  # متغير لحساب مجموع الديون
            debtors_count = 0  # متغير لعدد العملاء المدينين
            
            for i, row in enumerate(rows):
                # تنسيق التاريخ
                date_str = row[6]
                try:
                    date_obj = datetime.fromisoformat(date_str)
                    formatted_date = date_obj.strftime("%Y-%m-%d")
                except:
                    formatted_date = date_str[:10] if date_str else ""
                
                # تنسيق المديونية
                debt = row[4] or 0
                total_debt += debt  # إضافة الدين إلى المجموع
                if debt > 0:
                    debtors_count += 1  # عدد العملاء المدينين
                
                formatted_row = list(row[:6]) + [formatted_date]
                formatted_row[4] = f"{debt:,.2f} ج.م" if debt > 0 else "0.00 ج.م"
                
                # إضافة الصف مع التنسيق
                tags = ("evenrow" if i % 2 == 0 else "oddrow",)
                if debt > 0:
                    tags = tags + ("debt",)
                
                self.tree.insert("", "end", values=formatted_row, tags=tags)
            
            # تحديث العداد ومجموع الديون
            self.count_label.config(text=f"إجمالي العملاء: {len(rows)}")
            
            # تحديث إحصائيات الرأس
            self.update_header_stats(len(rows), total_debt, debtors_count)
            
            if search_term:
                self.update_status(f"عثر على {len(rows)} نتيجة")
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل البحث: {str(e)}")
    
    # ==================== تحديث شريط الحالة ====================
    def update_status(self, message):
        self.status_label.config(text=message)
        # إعادة تعيين الرسالة بعد 3 ثواني
        self.parent.after(3000, lambda: self.status_label.config(text="جاهز"))