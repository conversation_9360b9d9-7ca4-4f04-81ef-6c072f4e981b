import sqlite3
import os
from datetime import datetime

DATABASE_NAME = "sales_management.db"

def get_connection():
    """إنشاء اتصال بقاعدة البيانات مع تحسينات لمنع قفل قاعدة البيانات"""
    db_path = os.path.join(os.path.dirname(__file__), DATABASE_NAME)
    conn = sqlite3.connect(db_path, timeout=30)

    try:
        conn.execute("PRAGMA journal_mode=WAL;")
        conn.execute("PRAGMA synchronous=NORMAL;")
        conn.execute("PRAGMA busy_timeout=30000;")
    except Exception:
        pass

    return conn

def create_tables():
    """إنشاء جداول قاعدة البيانات مع ترقية المخطط إذا لزم"""
    with get_connection() as conn:
        cursor = conn.cursor()

        # جدول العملاء
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS customers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT NOT NULL,
                address TEXT,
                notes TEXT,
                debt_amount REAL DEFAULT 0,
                created_date TEXT,
                updated_date TEXT,
                status TEXT DEFAULT 'active'
            )
        """)

        # ترقية أعمدة جدول العملاء
        cursor.execute("PRAGMA table_info(customers)")
        cols = {row[1] for row in cursor.fetchall()}
        required_cols = [
            ("address", "TEXT", None),
            ("notes", "TEXT", None),
            ("debt_amount", "REAL", 0),
            ("created_date", "TEXT", None),
            ("updated_date", "TEXT", None),
            ("status", "TEXT", "'active'")
        ]
        for name, col_type, default in required_cols:
            if name not in cols:
                if default is None:
                    cursor.execute(f"ALTER TABLE customers ADD COLUMN {name} {col_type}")
                else:
                    cursor.execute(f"ALTER TABLE customers ADD COLUMN {name} {col_type} DEFAULT {default}")

        # جدول الموردين
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS suppliers (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                phone TEXT NOT NULL,
                address TEXT,
                company TEXT,
                tax_number TEXT,
                notes TEXT,
                debt_amount REAL DEFAULT 0,
                created_date TEXT,
                updated_date TEXT,
                status TEXT DEFAULT 'active'
            )
        """)

        # ترقية أعمدة جدول الموردين
        cursor.execute("PRAGMA table_info(suppliers)")
        cols = {row[1] for row in cursor.fetchall()}
        required_cols = [
            ("address", "TEXT", None),
            ("company", "TEXT", None),
            ("tax_number", "TEXT", None),
            ("notes", "TEXT", None),
            ("debt_amount", "REAL", 0),
            ("created_date", "TEXT", None),
            ("updated_date", "TEXT", None),
            ("status", "TEXT", "'active'")
        ]
        for name, col_type, default in required_cols:
            if name not in cols:
                if default is None:
                    cursor.execute(f"ALTER TABLE suppliers ADD COLUMN {name} {col_type}")
                else:
                    cursor.execute(f"ALTER TABLE suppliers ADD COLUMN {name} {col_type} DEFAULT {default}")

        # جدول المنتجات - إصدار محدث مع دعم الأعمدة المختلفة
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS products (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL,
                description TEXT DEFAULT '',
                cost_price REAL DEFAULT 0,
                selling_price REAL DEFAULT 0,
                price REAL DEFAULT 0,
                stock_quantity INTEGER DEFAULT 0,
                quantity INTEGER DEFAULT 0,
                updated_date TEXT,
                created_date TEXT,
                status TEXT DEFAULT 'active'
            )
        """)

        # ترقية أعمدة جدول المنتجات - إضافة جميع الأعمدة المطلوبة
        cursor.execute("PRAGMA table_info(products)")
        cols = {row[1] for row in cursor.fetchall()}
        
        required_cols = [
            ("description", "TEXT", "''"),
            ("cost_price", "REAL", "0"),
            ("selling_price", "REAL", "0"),
            ("price", "REAL", "0"),
            ("stock_quantity", "INTEGER", "0"),
            ("quantity", "INTEGER", "0"),
            ("updated_date", "TEXT", None),
            ("created_date", "TEXT", None),
            ("status", "TEXT", "'active'")
        ]
        
        for name, col_type, default in required_cols:
            if name not in cols:
                if default is None:
                    cursor.execute(f"ALTER TABLE products ADD COLUMN {name} {col_type}")
                else:
                    cursor.execute(f"ALTER TABLE products ADD COLUMN {name} {col_type} DEFAULT {default}")

        # إنشاء تريجرز لمزامنة الأعمدة المشتركة
        cursor.execute("""
            CREATE TRIGGER IF NOT EXISTS sync_price_on_insert
            AFTER INSERT ON products
            BEGIN
                UPDATE products SET 
                    price = CASE 
                        WHEN NEW.selling_price > 0 THEN NEW.selling_price 
                        WHEN NEW.price > 0 THEN NEW.price 
                        ELSE 0 
                    END,
                    selling_price = CASE 
                        WHEN NEW.selling_price > 0 THEN NEW.selling_price 
                        WHEN NEW.price > 0 THEN NEW.price 
                        ELSE 0 
                    END,
                    quantity = CASE 
                        WHEN NEW.stock_quantity >= 0 THEN NEW.stock_quantity 
                        WHEN NEW.quantity >= 0 THEN NEW.quantity 
                        ELSE 0 
                    END,
                    stock_quantity = CASE 
                        WHEN NEW.stock_quantity >= 0 THEN NEW.stock_quantity 
                        WHEN NEW.quantity >= 0 THEN NEW.quantity 
                        ELSE 0 
                    END
                WHERE id = NEW.id;
            END;
        """)

        cursor.execute("""
            CREATE TRIGGER IF NOT EXISTS sync_price_on_update
            AFTER UPDATE ON products
            BEGIN
                UPDATE products SET 
                    price = CASE 
                        WHEN NEW.selling_price > 0 THEN NEW.selling_price 
                        WHEN NEW.price > 0 THEN NEW.price 
                        ELSE OLD.price 
                    END,
                    selling_price = CASE 
                        WHEN NEW.selling_price > 0 THEN NEW.selling_price 
                        WHEN NEW.price > 0 THEN NEW.price 
                        ELSE OLD.selling_price 
                    END,
                    quantity = CASE 
                        WHEN NEW.stock_quantity >= 0 THEN NEW.stock_quantity 
                        WHEN NEW.quantity >= 0 THEN NEW.quantity 
                        ELSE OLD.quantity 
                    END,
                    stock_quantity = CASE 
                        WHEN NEW.stock_quantity >= 0 THEN NEW.stock_quantity 
                        WHEN NEW.quantity >= 0 THEN NEW.quantity 
                        ELSE OLD.stock_quantity 
                    END
                WHERE id = NEW.id;
            END;
        """)

        # باقي الجداول...
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS invoices (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_number TEXT UNIQUE NOT NULL,
                customer_id INTEGER,
                invoice_date TEXT NOT NULL,
                due_date TEXT,
                total_amount REAL NOT NULL,
                paid_amount REAL DEFAULT 0,
                discount REAL DEFAULT 0,
                tax_rate REAL DEFAULT 0,
                tax_amount REAL DEFAULT 0,
                status TEXT DEFAULT 'pending',
                notes TEXT,
                created_date TEXT,
                updated_date TEXT,
                FOREIGN KEY (customer_id) REFERENCES customers (id)
            )
        """)

        cursor.execute("""
            CREATE TABLE IF NOT EXISTS invoice_items (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_id INTEGER NOT NULL,
                product_id INTEGER NOT NULL,
                quantity INTEGER NOT NULL,
                unit_price REAL NOT NULL,
                total_price REAL NOT NULL,
                discount REAL DEFAULT 0,
                FOREIGN KEY (invoice_id) REFERENCES invoices (id) ON DELETE CASCADE,
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        """)

        cursor.execute("""
            CREATE TABLE IF NOT EXISTS payments (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                invoice_id INTEGER NOT NULL,
                amount REAL NOT NULL,
                payment_date TEXT NOT NULL,
                payment_method TEXT DEFAULT 'نقدي',
                notes TEXT,
                created_date TEXT,
                FOREIGN KEY (invoice_id) REFERENCES invoices (id)
            )
        """)

        cursor.execute("""
            CREATE TABLE IF NOT EXISTS stock_movements (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                product_id INTEGER NOT NULL,
                movement_type TEXT NOT NULL,
                quantity INTEGER NOT NULL,
                reference_type TEXT,
                reference_id INTEGER,
                notes TEXT,
                movement_date TEXT NOT NULL,
                created_date TEXT,
                FOREIGN KEY (product_id) REFERENCES products (id)
            )
        """)

        cursor.execute("""
            CREATE TABLE IF NOT EXISTS categories (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT NOT NULL UNIQUE,
                description TEXT,
                created_date TEXT
            )
        """)

        conn.commit()
        print("تم إنشاء قاعدة البيانات والجداول بنجاح!")

def add_sample_data():
    """إضافة بيانات تجريبية"""
    with get_connection() as conn:
        cursor = conn.cursor()

        cursor.execute("SELECT COUNT(*) FROM customers")
        if cursor.fetchone()[0] > 0:
            print("البيانات التجريبية موجودة مسبقاً")
            return

        try:
            # عملاء تجريبيين
            customers_data = [
                ("أحمد محمد", "0501234567", "الرياض، حي النخيل", "عميل مميز", 150.0, datetime.now().isoformat()),
                ("فاطمة علي", "0509876543", "جدة، حي الروضة", "عميل جديد", 0.0, datetime.now().isoformat()),
                ("محمد السعد", "0556789012", "الدمام، حي الفيصلية", "", 350.0, datetime.now().isoformat()),
                ("نور الهدى", "0512345678", "مكة، حي العزيزية", "عميلة دائمة", 0.0, datetime.now().isoformat()),
                ("خالد العتيبي", "0567890123", "الخبر، حي الثقبة", "", 275.5, datetime.now().isoformat())
            ]

            for customer in customers_data:
                cursor.execute("""
                    INSERT INTO customers (name, phone, address, notes, debt_amount, created_date)
                    VALUES (?, ?, ?, ?, ?, ?)
                """, customer)

            # موردين تجريبيين
            suppliers_data = [
                ("شركة النور للتجارة", "0114567890", "الرياض، حي الملز", "شركة النور", "1234567890", "مورد رئيسي", 500.0, datetime.now().isoformat()),
                ("مؤسسة الخليج", "0126789012", "جدة، حي الحمراء", "مؤسسة الخليج", "9876543210", "مورد ثانوي", 200.0, datetime.now().isoformat())
            ]

            for supplier in suppliers_data:
                cursor.execute("""
                    INSERT INTO suppliers (name, phone, address, company, tax_number, notes, debt_amount, created_date)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                """, supplier)

            # منتجات تجريبية - مع ضمان وجود جميع القيم المطلوبة
            products_data = [
                ("لاب توب ديل", "جهاز لاب توب 15 بوصة", 2000.0, 3000.0, 10),
                ("هاتف سامسونج", "هاتف ذكي 128 جيجابايت", 1500.0, 2500.0, 20),
                ("طابعة إتش بي", "طابعة ليزر ملونة", 800.0, 1200.0, 5)
            ]

            now = datetime.now().isoformat()
            for name, description, cost_price, selling_price, stock_quantity in products_data:
                cursor.execute("""
                    INSERT INTO products (name, description, cost_price, selling_price, price, stock_quantity, quantity, updated_date, created_date)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (name, description, cost_price, selling_price, selling_price, stock_quantity, stock_quantity, now, now))

            conn.commit()
            print("تم إضافة البيانات التجريبية بنجاح!")

        except Exception as e:
            print(f"خطأ في إضافة البيانات التجريبية: {e}")

def fix_existing_database():
    """إصلاح قاعدة البيانات الموجودة"""
    with get_connection() as conn:
        cursor = conn.cursor()
        
        try:
            # التحقق من وجود الأعمدة المطلوبة وإضافتها إذا لم تكن موجودة
            cursor.execute("PRAGMA table_info(products)")
            existing_cols = {row[1] for row in cursor.fetchall()}
            
            required_cols = {
                "description": "TEXT DEFAULT ''",
                "cost_price": "REAL DEFAULT 0",
                "selling_price": "REAL DEFAULT 0", 
                "price": "REAL DEFAULT 0",
                "stock_quantity": "INTEGER DEFAULT 0",
                "quantity": "INTEGER DEFAULT 0",
                "updated_date": "TEXT",
                "created_date": "TEXT",
                "status": "TEXT DEFAULT 'active'"
            }
            
            for col_name, col_def in required_cols.items():
                if col_name not in existing_cols:
                    cursor.execute(f"ALTER TABLE products ADD COLUMN {col_name} {col_def}")
                    print(f"تم إضافة العمود: {col_name}")
            
            # تحديث القيم الفارغة
            cursor.execute("""
                UPDATE products SET 
                    description = COALESCE(description, ''),
                    cost_price = COALESCE(cost_price, 0),
                    selling_price = COALESCE(selling_price, COALESCE(price, 0)),
                    price = COALESCE(price, COALESCE(selling_price, 0)),
                    stock_quantity = COALESCE(stock_quantity, COALESCE(quantity, 0)),
                    quantity = COALESCE(quantity, COALESCE(stock_quantity, 0)),
                    status = COALESCE(status, 'active')
                WHERE id IS NOT NULL
            """)
            
            conn.commit()
            print("تم إصلاح قاعدة البيانات بنجاح!")
            
        except Exception as e:
            print(f"خطأ في إصلاح قاعدة البيانات: {e}")

if __name__ == "__main__":
    create_tables()
    fix_existing_database()
    add_sample_data()