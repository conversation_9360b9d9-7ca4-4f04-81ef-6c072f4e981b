import tkinter as tk
from tkinter import ttk, messagebox
import database
from datetime import datetime
import sqlite3

class InventoryPage:
    def __init__(self, parent):
        self.parent = parent
        self.frame = tk.Frame(parent, bg="#f0f0f0")
        self.frame.pack(fill="both", expand=True)

        # رأس الصفحة
        self.create_header()

        # شريط الأدوات
        self.create_toolbar()

        # الجدول
        self.create_table()

        # شريط الحالة
        self.create_status_bar()

        # تحميل البيانات
        self.load_products()

    def create_header(self):
        """إنشاء رأس الصفحة"""
        header_frame = tk.Frame(self.frame, bg="#2c3e50", height=80)
        header_frame.pack(fill="x")
        header_frame.pack_propagate(False)

        # العنوان الرئيسي
        header_label = tk.Label(
            header_frame,
            text="إدارة المخزون",
            font=("Arial", 18, "bold"),
            bg="#2c3e50",
            fg="white"
        )
        header_label.pack(side="right", pady=15, padx=20)

        # إحصائيات المخزون
        stats_frame = tk.Frame(header_frame, bg="#2c3e50")
        stats_frame.pack(side="left", padx=20, pady=10)

        self.header_count_label = tk.Label(
            stats_frame,
            text="عدد المنتجات: 0",
            font=("Arial", 11, "bold"),
            bg="#2c3e50",
            fg="white"
        )
        self.header_count_label.pack(side="left", padx=10)

        self.header_quantity_label = tk.Label(
            stats_frame,
            text="إجمالي الكمية: 0",
            font=("Arial", 11, "bold"),
            bg="#2c3e50",
            fg="white"
        )
        self.header_quantity_label.pack(side="left", padx=10)

        self.header_value_label = tk.Label(
            stats_frame,
            text="قيمة المخزون: 0.00 ج.م",
            font=("Arial", 11, "bold"),
            bg="#2c3e50",
            fg="white"
        )
        self.header_value_label.pack(side="left", padx=10)

    def create_toolbar(self):
        """إنشاء شريط الأدوات"""
        toolbar_frame = tk.Frame(self.frame, bg="#ecf0f1", height=60)
        toolbar_frame.pack(fill="x", pady=(0, 10))
        toolbar_frame.pack_propagate(False)

        # أنماط الأزرار
        btn_style = {
            "font": ("Arial", 11, "bold"),
            "width": 12,
            "height": 2,
            "cursor": "hand2",
            "relief": "raised",
            "bd": 2
        }

        # زر الإضافة
        self.add_btn = tk.Button(
            toolbar_frame,
            text="➕ إضافة منتج",
            command=self.show_add_dialog,
            bg="#27ae60",
            fg="white",
            **btn_style
        )
        self.add_btn.pack(side="right", padx=10, pady=10)

        # زر التعديل
        self.edit_btn = tk.Button(
            toolbar_frame,
            text="✏️ تعديل",
            command=self.show_edit_dialog,
            bg="#3498db",
            fg="white",
            **btn_style
        )
        self.edit_btn.pack(side="right", padx=5, pady=10)

        # زر الحذف
        self.delete_btn = tk.Button(
            toolbar_frame,
            text="🗑️ حذف",
            command=self.delete_product,
            bg="#e74c3c",
            fg="white",
            **btn_style
        )
        self.delete_btn.pack(side="right", padx=5, pady=10)

        # زر التحديث
        self.refresh_btn = tk.Button(
            toolbar_frame,
            text="🔄 تحديث",
            command=self.load_products,
            bg="#95a5a6",
            fg="white",
            **btn_style
        )
        self.refresh_btn.pack(side="right", padx=5, pady=10)

        # شريط البحث
        search_frame = tk.Frame(toolbar_frame, bg="#ecf0f1")
        search_frame.pack(side="left", padx=20, pady=10)

        tk.Label(
            search_frame,
            text="🔍 بحث:",
            font=("Arial", 11),
            bg="#ecf0f1"
        ).pack(side="left", padx=5)

        self.search_entry = tk.Entry(search_frame, width=25, font=("Arial", 11))
        self.search_entry.pack(side="left")
        self.search_entry.bind("<KeyRelease>", self.search_products)

    def create_table(self):
        """إنشاء الجدول"""
        table_frame = tk.Frame(self.frame, bg="white")
        table_frame.pack(fill="both", expand=True, padx=10, pady=5)

        # تحسين ستايل الجدول
        style = ttk.Style()
        style.theme_use("clam")
        style.configure(
            "Treeview",
            background="white",
            fieldbackground="white",
            foreground="black",
            font=("Arial", 10),
            rowheight=30
        )
        style.configure(
            "Treeview.Heading",
            background="#34495e",
            foreground="white",
            font=("Arial", 11, "bold"),
            relief="flat"
        )
        style.map("Treeview", background=[("selected", "#3498db")])

        # إنشاء الجدول مع scrollbars
        tree_scroll_y = tk.Scrollbar(table_frame, orient="vertical")
        tree_scroll_x = tk.Scrollbar(table_frame, orient="horizontal")

        self.tree = ttk.Treeview(
            table_frame,
            columns=("id", "name", "cost_price", "selling_price", "stock_quantity", "total_value"),
            show="headings",
            yscrollcommand=tree_scroll_y.set,
            xscrollcommand=tree_scroll_x.set
        )

        tree_scroll_y.config(command=self.tree.yview)
        tree_scroll_x.config(command=self.tree.xview)
        tree_scroll_y.pack(side="right", fill="y")
        tree_scroll_x.pack(side="bottom", fill="x")
        self.tree.pack(fill="both", expand=True)

        # تكوين الأعمدة
        columns_config = [
            ("id", "الرقم", 50),
            ("name", "اسم المنتج", 200),
            ("cost_price", "سعر التكلفة", 100),
            ("selling_price", "سعر البيع", 100),
            ("stock_quantity", "الكمية", 80),
            ("total_value", "القيمة الإجمالية", 120)
        ]

        for col, heading, width in columns_config:
            self.tree.heading(col, text=heading)
            self.tree.column(col, width=width, anchor="center" if col in ["id", "cost_price", "selling_price", "stock_quantity", "total_value"] else "w")

        # إضافة الألوان للصفوف
        self.tree.tag_configure("oddrow", background="#f8f9fa")
        self.tree.tag_configure("evenrow", background="#ffffff")
        self.tree.tag_configure("low_stock", foreground="#e74c3c", font=("Arial", 10, "bold"))

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_frame = tk.Frame(self.frame, bg="#2c3e50", height=30)
        status_frame.pack(fill="x", side="bottom")
        status_frame.pack_propagate(False)

        self.status_label = tk.Label(
            status_frame,
            text="جاهز",
            font=("Arial", 10),
            bg="#2c3e50",
            fg="white"
        )
        self.status_label.pack(side="left", padx=10, pady=5)

        self.count_label = tk.Label(
            status_frame,
            text="إجمالي المنتجات: 0",
            font=("Arial", 10),
            bg="#2c3e50",
            fg="white"
        )
        self.count_label.pack(side="right", padx=10, pady=5)

    def load_products(self):
        """تحميل المنتجات من قاعدة البيانات"""
        for i in self.tree.get_children():
            self.tree.delete(i)

        try:
            with database.get_connection() as conn:
                cursor = conn.cursor()
                # تحديد أعمدة السعر والكمية المتاحة (selling_price/price و stock_quantity/quantity)
                cursor.execute("PRAGMA table_info(products)")
                cols = {row[1] for row in cursor.fetchall()}
                price_col = 'selling_price' if 'selling_price' in cols else ('price' if 'price' in cols else 'selling_price')
                qty_col = 'stock_quantity' if 'stock_quantity' in cols else ('quantity' if 'quantity' in cols else 'stock_quantity')

                query = f"""
                    SELECT id, name, cost_price, {price_col} as selling_price, {qty_col} as stock_quantity
                    FROM products
                    WHERE status = 'active'
                    ORDER BY id DESC
                """
                cursor.execute(query)
                rows = cursor.fetchall()

            total_products = len(rows)
            total_quantity = 0
            total_value = 0.0

            for i, row in enumerate(rows):
                product_id, name, cost_price, selling_price, stock_quantity = row
                cost_price = cost_price or 0
                selling_price = selling_price or 0
                stock_quantity = stock_quantity or 0

                # حساب القيمة الإجمالية للمنتج
                product_total_value = selling_price * stock_quantity
                total_quantity += stock_quantity
                total_value += product_total_value

                # تنسيق البيانات للعرض
                formatted_row = [
                    product_id,
                    name,
                    f"{cost_price:,.2f} ج.م",
                    f"{selling_price:,.2f} ج.م",
                    stock_quantity,
                    f"{product_total_value:,.2f} ج.م"
                ]

                # إضافة الصف مع التنسيق
                tags = ("evenrow" if i % 2 == 0 else "oddrow",)
                if stock_quantity == 0:
                    tags = tags + ("low_stock",)

                self.tree.insert("", "end", values=formatted_row, tags=tags)

            # تحديث الإحصائيات
            self.header_count_label.config(text=f"عدد المنتجات: {total_products}")
            self.header_quantity_label.config(text=f"إجمالي الكمية: {total_quantity}")
            self.header_value_label.config(text=f"قيمة المخزون: {total_value:,.2f} ج.م")
            self.count_label.config(text=f"إجمالي المنتجات: {total_products}")

            self.update_status(f"تم تحميل {total_products} منتج")

        except sqlite3.OperationalError as e:
            messagebox.showerror("خطأ في قاعدة البيانات", f"فشل تحميل البيانات: قاعدة البيانات مغلقة أو غير متاحة ({str(e)})")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل تحميل البيانات: {str(e)}")

    def show_add_dialog(self):
        """نافذة إضافة منتج جديد"""
        dialog = tk.Toplevel(self.parent)
        dialog.title("إضافة منتج جديد")
        dialog.geometry("500x400")
        dialog.configure(bg="#ecf0f1")
        dialog.resizable(False, False)
        dialog.grab_set()
        dialog.transient(self.parent)

        # رأس النافذة
        header = tk.Label(
            dialog,
            text="إضافة منتج جديد",
            font=("Arial", 16, "bold"),
            bg="#3498db",
            fg="white",
            pady=15
        )
        header.pack(fill="x")

        # إطار الحقول
        fields_frame = tk.Frame(dialog, bg="#ecf0f1", padx=20, pady=20)
        fields_frame.pack(fill="both", expand=True)

        # الحقول باستخدام grid
        entries = {}
        labels = [
            ("اسم المنتج *", "name", tk.Entry),
            ("الوصف", "description", tk.Text),
            ("سعر التكلفة", "cost_price", tk.Entry),
            ("سعر البيع *", "selling_price", tk.Entry),
            ("كمية المخزون", "stock_quantity", tk.Entry)
        ]

        for i, (label_text, field_name, widget_type) in enumerate(labels):
            label = tk.Label(
                fields_frame,
                text=label_text,
                font=("Arial", 11),
                bg="#ecf0f1",
                anchor="w"
            )
            label.grid(row=i, column=0, sticky="w", pady=5, padx=(0, 10))

            if widget_type == tk.Text:
                entry = widget_type(fields_frame, width=35, height=3, font=("Arial", 10))
            else:
                entry = widget_type(fields_frame, width=35, font=("Arial", 10))
            entry.grid(row=i, column=1, pady=5, sticky="w")
            entries[field_name] = entry

        # إطار الأزرار
        btn_frame = tk.Frame(dialog, bg="#ecf0f1", pady=15)
        btn_frame.pack(fill="x", side="bottom")

        def save_product():
            name = entries["name"].get().strip()
            description = entries["description"].get("1.0", "end-1c").strip() if isinstance(entries["description"], tk.Text) else entries["description"].get().strip()
            cost_price_text = entries["cost_price"].get().strip()
            selling_price_text = entries["selling_price"].get().strip()
            stock_quantity_text = entries["stock_quantity"].get().strip()

            # التحقق من الحقول المطلوبة
            if not name or not selling_price_text:
                messagebox.showerror("خطأ", "اسم المنتج وسعر البيع مطلوبان", parent=dialog)
                return

            try:
                cost_price = float(cost_price_text) if cost_price_text else 0.0
                selling_price = float(selling_price_text)
                stock_quantity = int(stock_quantity_text) if stock_quantity_text else 0

                # التحقق من القيم السالبة
                if cost_price < 0 or selling_price < 0 or stock_quantity < 0:
                    messagebox.showerror("خطأ", "الأسعار والكمية يجب ألا تكون سالبة", parent=dialog)
                    return

                with database.get_connection() as conn:
                    cursor = conn.cursor()
                    # التحقق من وجود أعمدة legacy: price و quantity
                    cursor.execute("PRAGMA table_info(products)")
                    columns = {row[1] for row in cursor.fetchall()}
                    now = datetime.now().isoformat()

                    has_price = 'price' in columns
                    has_selling = 'selling_price' in columns
                    has_quantity = 'quantity' in columns
                    has_stock_quantity = 'stock_quantity' in columns

                    if has_price and not has_selling and has_quantity and not has_stock_quantity:
                        # قاعدة قديمة جداً: price + quantity فقط
                        cursor.execute(
                            """
                            INSERT INTO products
                            (name, description, cost_price, price, quantity, updated_date, status)
                            VALUES (?, ?, ?, ?, ?, ?, 'active')
                            """,
                            (name, description, cost_price, selling_price, stock_quantity, now)
                        )
                    elif has_price and not has_selling and has_stock_quantity:
                        # price موجود وstock_quantity موجود
                        cursor.execute(
                            """
                            INSERT INTO products
                            (name, description, cost_price, stock_quantity, updated_date, status, price)
                            VALUES (?, ?, ?, ?, ?, 'active', ?)
                            """,
                            (name, description, cost_price, stock_quantity, now, selling_price)
                        )
                    elif has_selling and not has_stock_quantity and has_quantity:
                        # selling_price موجود وquantity هو عمود الكمية
                        cursor.execute(
                            """
                            INSERT INTO products
                            (name, description, cost_price, selling_price, quantity, updated_date, status)
                            VALUES (?, ?, ?, ?, ?, ?, 'active')
                            """,
                            (name, description, cost_price, selling_price, stock_quantity, now)
                        )
                    else:
                        # المخطط الحديث: selling_price + stock_quantity
                        cursor.execute(
                            """
                            INSERT INTO products
                            (name, description, cost_price, selling_price, stock_quantity, updated_date, status)
                            VALUES (?, ?, ?, ?, ?, ?, 'active')
                            """,
                            (name, description, cost_price, selling_price, stock_quantity, now)
                        )
                    conn.commit()

                self.load_products()
                self.update_status(f"تم إضافة المنتج: {name}")
                dialog.destroy()
                messagebox.showinfo("نجاح", "تم إضافة المنتج بنجاح")

            except sqlite3.OperationalError as e:
                messagebox.showerror("خطأ في قاعدة البيانات", f"فشل حفظ المنتج: قاعدة البيانات مغلقة أو غير متاحة ({str(e)})")
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال أرقام صحيحة للأسعار والكمية", parent=dialog)
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ: {str(e)}", parent=dialog)

        save_btn = tk.Button(
            btn_frame,
            text="💾 حفظ",
            command=save_product,
            bg="#27ae60",
            fg="white",
            font=("Arial", 11, "bold"),
            width=12,
            height=2,
            cursor="hand2",
            relief="raised",
            bd=2
        )
        save_btn.pack(side="right", padx=10)

        cancel_btn = tk.Button(
            btn_frame,
            text="❌ إلغاء",
            command=dialog.destroy,
            bg="#e74c3c",
            fg="white",
            font=("Arial", 11, "bold"),
            width=12,
            height=2,
            cursor="hand2",
            relief="raised",
            bd=2
        )
        cancel_btn.pack(side="right", padx=10)

        # التركيز على أول حقل
        entries["name"].focus()

    def show_edit_dialog(self):
        """نافذة تعديل منتج"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("تنبيه", "الرجاء اختيار منتج للتعديل")
            return

        item = self.tree.item(selected)
        values = item["values"]
        product_id = values[0]

        # استرجاع البيانات الكاملة من قاعدة البيانات
        try:
            with database.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute("SELECT * FROM products WHERE id = ?", (product_id,))
                product_data = cursor.fetchone()

            if not product_data:
                messagebox.showerror("خطأ", "لم يتم العثور على المنتج")
                return

        except sqlite3.OperationalError as e:
            messagebox.showerror("خطأ في قاعدة البيانات", f"فشل استرجاع البيانات: قاعدة البيانات مغلقة أو غير متاحة ({str(e)})")
            return
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل استرجاع البيانات: {str(e)}")
            return

        dialog = tk.Toplevel(self.parent)
        dialog.title("تعديل بيانات المنتج")
        dialog.geometry("500x400")
        dialog.configure(bg="#ecf0f1")
        dialog.resizable(False, False)
        dialog.grab_set()
        dialog.transient(self.parent)

        # رأس النافذة
        header = tk.Label(
            dialog,
            text="تعديل بيانات المنتج",
            font=("Arial", 16, "bold"),
            bg="#3498db",
            fg="white",
            pady=15
        )
        header.pack(fill="x")

        # إطار الحقول
        fields_frame = tk.Frame(dialog, bg="#ecf0f1", padx=20, pady=20)
        fields_frame.pack(fill="both", expand=True)

        # الحقول مع القيم الحالية
        entries = {}
        labels = [
            ("اسم المنتج *", "name", tk.Entry, product_data[1] or ""),
            ("الوصف", "description", tk.Text, product_data[2] or ""),
            ("سعر التكلفة", "cost_price", tk.Entry, str(product_data[3] or 0)),
            ("سعر البيع *", "selling_price", tk.Entry, str(product_data[4] or 0)),
            ("كمية المخزون", "stock_quantity", tk.Entry, str(product_data[5] or 0))
        ]

        for i, (label_text, field_name, widget_type, value) in enumerate(labels):
            label = tk.Label(
                fields_frame,
                text=label_text,
                font=("Arial", 11),
                bg="#ecf0f1",
                anchor="w"
            )
            label.grid(row=i, column=0, sticky="w", pady=5, padx=(0, 10))

            if widget_type == tk.Text:
                entry = widget_type(fields_frame, width=35, height=3, font=("Arial", 10))
                entry.insert("1.0", value)
            else:
                entry = widget_type(fields_frame, width=35, font=("Arial", 10))
                entry.insert(0, value)
            entry.grid(row=i, column=1, pady=5, sticky="w")
            entries[field_name] = entry

        # إطار الأزرار
        btn_frame = tk.Frame(dialog, bg="#ecf0f1", pady=15)
        btn_frame.pack(fill="x", side="bottom")

        def update_product():
            name = entries["name"].get().strip()
            description = entries["description"].get("1.0", "end-1c").strip() if isinstance(entries["description"], tk.Text) else entries["description"].get().strip()
            cost_price_text = entries["cost_price"].get().strip()
            selling_price_text = entries["selling_price"].get().strip()
            stock_quantity_text = entries["stock_quantity"].get().strip()

            if not name or not selling_price_text:
                messagebox.showerror("خطأ", "اسم المنتج وسعر البيع مطلوبان", parent=dialog)
                return

            try:
                cost_price = float(cost_price_text) if cost_price_text else 0.0
                selling_price = float(selling_price_text)
                stock_quantity = int(stock_quantity_text) if stock_quantity_text else 0

                # التحقق من القيم السالبة
                if cost_price < 0 or selling_price < 0 or stock_quantity < 0:
                    messagebox.showerror("خطأ", "الأسعار والكمية يجب ألا تكون سالبة", parent=dialog)
                    return

                with database.get_connection() as conn:
                    cursor = conn.cursor()
                    # دعم جداول قديمة تحتوي على عمود price
                    cursor.execute("PRAGMA table_info(products)")
                    cols = {row[1] for row in cursor.fetchall()}
                    now = datetime.now().isoformat()

                    if 'price' in cols and 'selling_price' not in cols:
                        cursor.execute(
                            """
                            UPDATE products
                            SET name=?, description=?, cost_price=?, price=?, stock_quantity=?, updated_date=?
                            WHERE id=?
                            """,
                            (name, description, cost_price, selling_price, stock_quantity, now, product_id)
                        )
                    else:
                        cursor.execute(
                            """
                            UPDATE products
                            SET name=?, description=?, cost_price=?, selling_price=?, stock_quantity=?, updated_date=?
                            WHERE id=?
                            """,
                            (name, description, cost_price, selling_price, stock_quantity, now, product_id)
                        )
                    conn.commit()

                self.load_products()
                self.update_status(f"تم تحديث المنتج: {name}")
                dialog.destroy()
                messagebox.showinfo("نجاح", "تم تحديث البيانات بنجاح")

            except sqlite3.OperationalError as e:
                messagebox.showerror("خطأ في قاعدة البيانات", f"فشل تحديث المنتج: قاعدة البيانات مغلقة أو غير متاحة ({str(e)})")
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال أرقام صحيحة للأسعار والكمية", parent=dialog)
            except Exception as e:
                messagebox.showerror("خطأ", f"حدث خطأ أثناء التحديث: {str(e)}", parent=dialog)

        update_btn = tk.Button(
            btn_frame,
            text="💾 تحديث",
            command=update_product,
            bg="#27ae60",
            fg="white",
            font=("Arial", 11, "bold"),
            width=12,
            height=2,
            cursor="hand2",
            relief="raised",
            bd=2
        )
        update_btn.pack(side="right", padx=10)

        cancel_btn = tk.Button(
            btn_frame,
            text="❌ إلغاء",
            command=dialog.destroy,
            bg="#e74c3c",
            fg="white",
            font=("Arial", 11, "bold"),
            width=12,
            height=2,
            cursor="hand2",
            relief="raised",
            bd=2
        )
        cancel_btn.pack(side="right", padx=10)

    def delete_product(self):
        """حذف منتج"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("تنبيه", "الرجاء اختيار منتج للحذف")
            return

        item = self.tree.item(selected)
        product_name = item["values"][1]
        product_id = item["values"][0]

        result = messagebox.askyesno(
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف المنتج:\n{product_name}؟",
            icon="warning"
        )

        if result:
            try:
                with database.get_connection() as conn:
                    cursor = conn.cursor()
                    cursor.execute("UPDATE products SET status = 'inactive' WHERE id=?", (product_id,))
                    conn.commit()

                self.load_products()
                self.update_status(f"تم حذف المنتج: {product_name}")
                messagebox.showinfo("نجاح", "تم حذف المنتج بنجاح")

            except sqlite3.OperationalError as e:
                messagebox.showerror("خطأ في قاعدة البيانات", f"فشل حذف المنتج: قاعدة البيانات مغلقة أو غير متاحة ({str(e)})")
            except Exception as e:
                messagebox.showerror("خطأ", f"فشل حذف المنتج: {str(e)}")

    def search_products(self, event=None):
        """البحث في المنتجات"""
        search_term = self.search_entry.get().strip().lower()

        for item in self.tree.get_children():
            self.tree.delete(item)

        try:
            with database.get_connection() as conn:
                cursor = conn.cursor()
                # تحديد عمود سعر البيع المتاح (selling_price أو price)
                cursor.execute("PRAGMA table_info(products)")
                cols = {row[1] for row in cursor.fetchall()}
                has_selling = 'selling_price' in cols
                has_price = 'price' in cols
                price_col = 'selling_price' if has_selling else ('price' if has_price else 'selling_price')

                if search_term:
                    query = f"""
                        SELECT id, name, cost_price, {price_col} as selling_price, stock_quantity
                        FROM products
                        WHERE status = 'active' AND (
                            LOWER(name) LIKE ? OR
                            LOWER(description) LIKE ?
                        )
                        ORDER BY id DESC
                    """
                    cursor.execute(query, (f"%{search_term}%", f"%{search_term}%"))
                else:
                    query = f"""
                        SELECT id, name, cost_price, {price_col} as selling_price, stock_quantity
                        FROM products
                        WHERE status = 'active'
                        ORDER BY id DESC
                    """
                    cursor.execute(query)

                rows = cursor.fetchall()

            total_products = len(rows)
            total_quantity = 0
            total_value = 0.0

            for i, row in enumerate(rows):
                product_id, name, cost_price, selling_price, stock_quantity = row
                cost_price = cost_price or 0
                selling_price = selling_price or 0
                stock_quantity = stock_quantity or 0

                product_total_value = selling_price * stock_quantity
                total_quantity += stock_quantity
                total_value += product_total_value

                formatted_row = [
                    product_id,
                    name,
                    f"{cost_price:,.2f} ج.م",
                    f"{selling_price:,.2f} ج.م",
                    stock_quantity,
                    f"{product_total_value:,.2f} ج.م"
                ]

                # إضافة الصف مع التنسيق
                tags = ("evenrow" if i % 2 == 0 else "oddrow",)
                if stock_quantity == 0:
                    tags = tags + ("low_stock",)

                self.tree.insert("", "end", values=formatted_row, tags=tags)

            # تحديث الإحصائيات
            self.header_count_label.config(text=f"عدد المنتجات: {total_products}")
            self.header_quantity_label.config(text=f"إجمالي الكمية: {total_quantity}")
            self.header_value_label.config(text=f"قيمة المخزون: {total_value:,.2f} ج.م")
            self.count_label.config(text=f"إجمالي المنتجات: {total_products}")

            if search_term:
                self.update_status(f"عثر على {total_products} نتيجة")

        except sqlite3.OperationalError as e:
            messagebox.showerror("خطأ في قاعدة البيانات", f"فشل البحث: قاعدة البيانات مغلقة أو غير متاحة ({str(e)})")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل البحث: {str(e)}")

    def update_status(self, message):
        """تحديث شريط الحالة"""
        self.status_label.config(text=message)
        self.parent.after(3000, lambda: self.status_label.config(text="جاهز"))