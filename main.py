import tkinter as tk
from tkinter import ttk
from tkinter import font
import random
import math
import styles
from customers.customers import CustomersPage
from suppliers.suppliers import SuppliersPage
from inventory.inventory import InventoryPage
from invoices.invoices import InvoicesPage
from sales_invoice import SalesInvoicePage  # إضافة الاستيراد
import database

class Star:
    def __init__(self, canvas, x, y, size, speed):
        self.canvas = canvas
        self.x = x
        self.y = y
        self.size = size
        self.speed = speed
        self.angle = random.uniform(0, 2 * math.pi)
        self.twinkle_phase = random.uniform(0, 2 * math.pi)
        self.star_id = None
        self.create_star()
    
    def create_star(self):
        # إنشاء نجمة مع تأثير التوهج
        brightness = 0.3 + 0.7 * (math.sin(self.twinkle_phase) + 1) / 2
        color = f"#{int(255 * brightness):02x}{int(255 * brightness):02x}{int(200 * brightness + 55):02x}"
        
        self.star_id = self.canvas.create_oval(
            self.x - self.size, self.y - self.size,
            self.x + self.size, self.y + self.size,
            fill=color, outline="", width=0
        )
    
    def move(self):
        # حركة النجمة ببطء
        self.x += math.cos(self.angle) * self.speed
        self.y += math.sin(self.angle) * self.speed
        self.twinkle_phase += 0.1
        
        # إعادة تدوير النجمة عند الخروج من الشاشة
        canvas_width = self.canvas.winfo_width()
        canvas_height = self.canvas.winfo_height()
        
        if self.x < -10:
            self.x = canvas_width + 10
        elif self.x > canvas_width + 10:
            self.x = -10
        if self.y < -10:
            self.y = canvas_height + 10
        elif self.y > canvas_height + 10:
            self.y = -10
    
    def update(self):
        if self.star_id:
            self.move()
            brightness = 0.3 + 0.7 * (math.sin(self.twinkle_phase) + 1) / 2
            color = f"#{int(255 * brightness):02x}{int(255 * brightness):02x}{int(200 * brightness + 55):02x}"
            
            self.canvas.coords(
                self.star_id,
                self.x - self.size, self.y - self.size,
                self.x + self.size, self.y + self.size
            )
            self.canvas.itemconfig(self.star_id, fill=color)

class ModernButton(tk.Frame):
    def __init__(self, parent, text, command, icon="", night_mode=False, **kwargs):
        super().__init__(parent, **kwargs)

        self.command = command
        self.is_active = False
        self.night_mode = night_mode

        # تحديد الألوان حسب الوضع
        if night_mode:
            self.bg_normal = "#1a1a2e"
            self.bg_hover = "#16213e"
            self.bg_active = "#0f3460"
            self.fg_normal = "#e94560"
            self.fg_active = "#ffffff"
        else:
            self.bg_normal = styles.SIDEBAR_BG
            self.bg_hover = styles.SIDEBAR_HOVER
            self.bg_active = styles.SIDEBAR_ACTIVE
            self.fg_normal = styles.SIDEBAR_TEXT
            self.fg_active = styles.TEXT_WHITE

        # إعداد الإطار
        self.configure(
            bg=self.bg_normal,
            cursor="hand2",
            padx=10,
            pady=5,
            relief=tk.FLAT,
            borderwidth=0
        )
        self.pack_propagate(False)

        # إطار المحتوى
        self.content_frame = tk.Frame(
            self,
            bg=self.bg_normal,
            padx=10,
            pady=5
        )
        self.content_frame.pack(expand=True, fill="both")

        # أيقونة الزر
        if icon:
            self.icon_label = tk.Label(
                self.content_frame,
                text=icon,
                font=styles.ICON_FONT,
                bg=self.bg_normal,
                fg=self.fg_normal
            )
            self.icon_label.pack(side=tk.LEFT, padx=5)

        # نص الزر
        self.text_label = tk.Label(
            self.content_frame,
            text=text,
            font=styles.FONT_MEDIUM,
            bg=self.bg_normal,
            fg=self.fg_normal,
            wraplength=150
        )
        self.text_label.pack(side=tk.RIGHT, expand=True, fill=tk.X)

        # ربط الأحداث
        self.bind_events()

    def bind_events(self):
        def on_click(e):
            self.command()

        def on_enter(e):
            if not self.is_active:
                self.configure(bg=self.bg_hover)
                self.content_frame.configure(bg=self.bg_hover)
                self.text_label.configure(bg=self.bg_hover)
                if hasattr(self, 'icon_label'):
                    self.icon_label.configure(bg=self.bg_hover)

        def on_leave(e):
            if not self.is_active:
                self.configure(bg=self.bg_normal)
                self.content_frame.configure(bg=self.bg_normal)
                self.text_label.configure(bg=self.bg_normal)
                if hasattr(self, 'icon_label'):
                    self.icon_label.configure(bg=self.bg_normal)

        # ربط الأحداث بجميع العناصر
        widgets = [self, self.content_frame, self.text_label]
        if hasattr(self, 'icon_label'):
            widgets.append(self.icon_label)

        for widget in widgets:
            widget.bind("<Button-1>", on_click)
            widget.bind("<Enter>", on_enter)
            widget.bind("<Leave>", on_leave)

    def set_active(self, active=True):
        self.is_active = active
        if active:
            bg_color = self.bg_active
            fg_color = self.fg_active
        else:
            bg_color = self.bg_normal
            fg_color = self.fg_normal

        self.configure(bg=bg_color)
        self.content_frame.configure(bg=bg_color)
        self.text_label.configure(bg=bg_color, fg=fg_color)
        if hasattr(self, 'icon_label'):
            self.icon_label.configure(bg=bg_color, fg=fg_color)

class SalesManagementApp:
    def __init__(self, root):
        self.root = root
        self.root.title("نظام إدارة المبيعات")
        self.root.geometry("1200x700")
        self.root.state('zoomed')
        
        # متغير تتبع الوضع الليلي
        self.night_mode = False
        self.stars = []
        self.animation_id = None
        
        # متغير لتتبع الصفحة النشطة
        self.active_button = None

        # تطبيق التنسيقات
        self.apply_theme()
        self.setup_styles()

        # إنشاء الواجهة الرئيسية
        self.create_background()
        self.create_header()
        self.create_main_layout()
        self.create_sidebar()
        self.create_content_area()
        self.create_status_bar()

        # عرض الصفحة الافتراضية
        self.show_customers()

    def apply_theme(self):
        """تطبيق السمة حسب الوضع"""
        if self.night_mode:
            self.bg_color = "#0f0f23"
            self.primary_color = "#1a1a2e"
            self.secondary_color = "#16213e"
            self.text_color = "#e94560"
            self.accent_color = "#f5f5f5"
            self.card_color = "#1a1a2e"
        else:
            self.bg_color = styles.BACKGROUND_COLOR
            self.primary_color = styles.PRIMARY_COLOR
            self.secondary_color = styles.SECONDARY_COLOR
            self.text_color = styles.TEXT_COLOR
            self.accent_color = styles.ACCENT_COLOR
            self.card_color = styles.CARD_COLOR

        self.root.configure(bg=self.bg_color)

    def create_background(self):
        """إنشاء خلفية مع النجوم للوضع الليلي"""
        self.bg_canvas = tk.Canvas(
            self.root,
            bg=self.bg_color,
            highlightthickness=0
        )
        self.bg_canvas.place(x=0, y=0, relwidth=1, relheight=1)
        
        if self.night_mode:
            self.create_stars()
            self.animate_stars()

    def create_stars(self):
        """إنشاء النجوم المتحركة"""
        self.stars = []
        num_stars = 50
        
        # انتظار حتى يتم رسم النافذة للحصول على الأبعاد الصحيحة
        self.root.update_idletasks()
        width = self.root.winfo_width()
        height = self.root.winfo_height()
        
        for _ in range(num_stars):
            x = random.randint(0, width)
            y = random.randint(0, height)
            size = random.uniform(1, 3)
            speed = random.uniform(0.1, 0.5)
            star = Star(self.bg_canvas, x, y, size, speed)
            self.stars.append(star)

    def animate_stars(self):
        """تحريك النجوم"""
        if self.night_mode and self.stars:
            for star in self.stars:
                star.update()
            self.animation_id = self.root.after(100, self.animate_stars)

    def stop_animation(self):
        """إيقاف تحريك النجوم"""
        if self.animation_id:
            self.root.after_cancel(self.animation_id)
            self.animation_id = None

    def toggle_night_mode(self):
        """تبديل الوضع الليلي"""
        self.night_mode = not self.night_mode
        
        if self.night_mode:
            self.stop_animation()
            
        self.apply_theme()
        self.recreate_interface()

    def recreate_interface(self):
        """إعادة إنشاء الواجهة مع السمة الجديدة"""
        # حفظ الصفحة النشطة
        current_page = self.active_button
        
        # مسح كل شيء
        for widget in self.root.winfo_children():
            widget.destroy()
        
        self.stars = []
        self.stop_animation()
        
        # إعادة إنشاء الواجهة
        self.create_background()
        self.create_header()
        self.create_main_layout()
        self.create_sidebar()
        self.create_content_area()
        self.create_status_bar()
        
        # استعادة الصفحة النشطة
        if current_page:
            if current_page == "العملاء":
                self.show_customers()
            elif current_page == "الموردين":
                self.show_suppliers()
            elif current_page == "المخزون":
                self.show_inventory()
            elif current_page == "الفواتير":
                self.show_invoices()
            elif current_page == "فاتورة المبيعات":
                self.show_sales_invoice()
            elif current_page == "فاتورة المشتريات":
                self.show_purchase_invoice()
            elif current_page == "فاتورة الاستخلاص":
                self.show_returns()
            elif current_page == "التقارير":
                self.show_reports()
        else:
            self.show_customers()

    def setup_styles(self):
        style = ttk.Style()
        style.theme_use("clam")

        # تنسيقات حسب الوضع
        if self.night_mode:
            bg_color = "#1a1a2e"
            fg_color = "#e94560"
            select_bg = "#16213e"
        else:
            bg_color = styles.BACKGROUND_COLOR
            fg_color = styles.TEXT_COLOR
            select_bg = styles.PRIMARY_COLOR

        style.configure("TLabel",
                       background=bg_color,
                       foreground=fg_color,
                       font=styles.FONT_MEDIUM)

        style.configure("TButton",
                       font=styles.FONT_MEDIUM,
                       padding=(20, 10),
                       relief="flat")

        style.configure("Treeview",
                       font=styles.FONT_SMALL,
                       rowheight=30,
                       fieldbackground=bg_color)

        style.configure("Treeview.Heading",
                       font=styles.FONT_MEDIUM,
                       background=select_bg,
                       foreground="white",
                       relief="flat")

    def create_header(self):
        """إنشاء الشريط العلوي"""
        self.header = tk.Frame(
            self.root,
            bg=self.primary_color,
            height=80
        )
        self.header.pack(fill=tk.X)
        self.header.pack_propagate(False)

        # عنوان التطبيق
        title_label = tk.Label(
            self.header,
            text="🏢 نظام إدارة المبيعات",
            font=styles.FONT_TITLE,
            bg=self.primary_color,
            fg="white"
        )
        title_label.pack(side=tk.RIGHT, padx=30, pady=20)

        # معلومات إضافية
        info_label = tk.Label(
            self.header,
            text="إدارة شاملة للمبيعات والمخزون",
            font=styles.FONT_SMALL,
            bg=self.primary_color,
            fg="#cccccc"
        )
        info_label.pack(side=tk.RIGHT, padx=(0, 30))

        # زر تبديل الوضع الليلي
        mode_icon = "🌙" if not self.night_mode else "☀️"
        mode_text = "الوضع الليلي" if not self.night_mode else "الوضع النهاري"
        
        self.mode_btn = tk.Button(
            self.header,
            text=f"{mode_icon} {mode_text}",
            font=("Arial", 10, "bold"),
            bg=self.secondary_color,
            fg="white",
            bd=0,
            relief=tk.FLAT,
            padx=15,
            pady=8,
            cursor="hand2",
            command=self.toggle_night_mode
        )
        self.mode_btn.pack(side=tk.LEFT, padx=20, pady=20)

    def create_main_layout(self):
        """إنشاء التخطيط الرئيسي"""
        self.main_frame = tk.Frame(
            self.root,
            bg=self.bg_color,
            padx=20,
            pady=20
        )
        self.main_frame.pack(fill=tk.BOTH, expand=True)

    def on_sidebar_mousewheel(self, event):
        """التعامل مع عجلة الماوس في الشريط الجانبي"""
        self.sidebar_canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        return "break"

    def bind_sidebar_mousewheel(self, widget):
        """ربط عجلة الماوس مع widget معين"""
        widget.bind("<Enter>", lambda e: widget.bind_all('<MouseWheel>', self.on_sidebar_mousewheel))
        widget.bind("<Leave>", lambda e: widget.unbind_all('<MouseWheel>'))

    def create_sidebar(self):
        """إنشاء الشريط الجانبي القابل للتمرير"""
        sidebar_bg = self.primary_color if self.night_mode else styles.DARK_COLOR
        
        # الإطار الخارجي للشريط الجانبي
        self.sidebar = tk.Frame(
            self.main_frame,
            bg=sidebar_bg,
            width=250
        )
        self.sidebar.pack(side=tk.RIGHT, fill=tk.Y, padx=(0, 2))
        self.sidebar.pack_propagate(False)

        # Canvas للتمرير
        self.sidebar_canvas = tk.Canvas(
            self.sidebar,
            bg=sidebar_bg,
            width=250,
            highlightthickness=0
        )
        self.sidebar_canvas.pack(side="left", fill="both", expand=True)

        # Scrollbar للشريط الجانبي
        self.sidebar_scrollbar = ttk.Scrollbar(
            self.sidebar,
            orient="vertical",
            command=self.sidebar_canvas.yview
        )
        self.sidebar_scrollbar.pack(side="right", fill="y")

        # ربط Canvas مع Scrollbar
        self.sidebar_canvas.configure(yscrollcommand=self.sidebar_scrollbar.set)

        # إطار المحتوى داخل Canvas
        self.sidebar_frame = tk.Frame(
            self.sidebar_canvas,
            bg=sidebar_bg
        )

        # إنشاء نافذة في Canvas
        self.sidebar_window = self.sidebar_canvas.create_window(
            (0, 0),
            window=self.sidebar_frame,
            anchor="nw"
        )

        # عنوان الشريط الجانبي
        sidebar_title = tk.Label(
            self.sidebar_frame,
            text="القوائم الرئيسية",
            font=styles.FONT_LARGE,
            bg=sidebar_bg,
            fg="white",
            pady=20
        )
        sidebar_title.pack(fill=tk.X)

        # خط فاصل
        separator_color = "#e94560" if self.night_mode else styles.PRIMARY_COLOR
        separator = tk.Frame(
            self.sidebar_frame,
            height=2,
            bg=separator_color
        )
        separator.pack(fill=tk.X, padx=20, pady=(0, 20))

        # قائمة الأزرار
        self.buttons = {}

        button_configs = [
            ("العملاء", "👥", self.show_customers),
            ("الموردين", "🏪", self.show_suppliers),
            ("المخزون", "📦", self.show_inventory),
            ("الفواتير", "📋", self.show_invoices),
            ("فاتورة المبيعات", "🧾", self.show_sales_invoice),
            ("فاتورة المشتريات", "📝", self.show_purchase_invoice),
            ("فاتورة الاستخلاص", "↩️", self.show_returns),
            ("التقارير", "📊", self.show_reports),
        ]

        for text, icon, command in button_configs:
            btn = ModernButton(
                self.sidebar_frame,
                text=text,
                command=command,
                icon=icon,
                night_mode=self.night_mode,
                height=60,
                width=230
            )
            btn.pack(pady=5, padx=10, fill=tk.X)
            self.buttons[text] = btn

        # تحديث منطقة التمرير
        def configure_scroll_region(event=None):
            self.sidebar_canvas.configure(scrollregion=self.sidebar_canvas.bbox("all"))
            # ضبط عرض النافذة
            canvas_width = self.sidebar_canvas.winfo_width()
            self.sidebar_canvas.itemconfig(self.sidebar_window, width=canvas_width)

        self.sidebar_frame.bind('<Configure>', configure_scroll_region)
        self.sidebar_canvas.bind('<Configure>', configure_scroll_region)

        # ربط عجلة الماوس مع الشريط الجانبي
        self.bind_sidebar_mousewheel(self.sidebar_canvas)
        self.bind_sidebar_mousewheel(self.sidebar_frame)
        
        # ربط عجلة الماوس مع جميع الأزرار
        for btn in self.buttons.values():
            self.bind_sidebar_mousewheel(btn)

    def create_content_area(self):
        """إنشاء منطقة المحتوى"""
        content_container = tk.Frame(
            self.main_frame,
            bg=self.bg_color
        )
        content_container.pack(
            side=tk.LEFT,
            fill=tk.BOTH,
            expand=True,
            padx=20,
            pady=20
        )

        # إطار المحتوى الرئيسي
        self.content_frame = tk.Frame(
            content_container,
            bg=self.card_color,
            relief=tk.RAISED,
            bd=1,
            padx=20,
            pady=20
        )
        self.content_frame.pack(fill=tk.BOTH, expand=True)

    def create_status_bar(self):
        """إنشاء شريط الحالة"""
        status_bg = self.primary_color if self.night_mode else styles.DARK_COLOR
        
        self.status_bar = tk.Frame(
            self.root,
            bg=status_bg,
            height=30
        )
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        self.status_bar.pack_propagate(False)

        # معلومات الحالة
        self.status_label = tk.Label(
            self.status_bar,
            text="جاهز",
            font=styles.FONT_SMALL,
            bg=status_bg,
            fg="white"
        )
        self.status_label.pack(side=tk.RIGHT, padx=10, pady=5)

        # معلومات إضافية
        mode_status = " | الوضع الليلي 🌙" if self.night_mode else " | الوضع النهاري ☀️"
        self.info_label = tk.Label(
            self.status_bar,
            text=f"نظام إدارة المبيعات | جميع الحقوق محفوظة{mode_status}",
            font=styles.FONT_TINY,
            bg=status_bg,
            fg="#cccccc"
        )
        self.info_label.pack(side=tk.LEFT, padx=10, pady=5)

    def set_active_button(self, button_text):
        """تعيين الزر النشط"""
        if self.active_button:
            self.buttons[self.active_button].set_active(False)

        if button_text in self.buttons:
            self.buttons[button_text].set_active(True)
            self.active_button = button_text

    def clear_content(self):
        """مسح محتوى الصفحة"""
        for widget in self.content_frame.winfo_children():
            widget.destroy()

    def update_title(self, title):
        """تحديث عنوان الصفحة"""
        title_label = tk.Label(
            self.content_frame,
            text=title,
            font=styles.FONT_LARGE,
            bg=self.card_color,
            fg=self.text_color,
            anchor="w"
        )
        title_label.pack(fill=tk.X, pady=(0, 20))

    def update_status(self, status):
        """تحديث شريط الحالة"""
        self.status_label.configure(text=status)

    def show_customers(self):
        self.clear_content()
        self.update_title("👥 إدارة العملاء")
        self.update_status("عرض بيانات العملاء")
        self.set_active_button("العملاء")
        CustomersPage(self.content_frame)

    def show_suppliers(self):
        self.clear_content()
        self.update_title("🏪 إدارة الموردين")
        self.update_status("عرض بيانات الموردين")
        self.set_active_button("الموردين")
        SuppliersPage(self.content_frame)

    def show_inventory(self):
        self.clear_content()
        self.update_title("📦 إدارة المخزون")
        self.update_status("عرض بيانات المخزون")
        self.set_active_button("المخزون")
        InventoryPage(self.content_frame)

    def show_invoices(self):
        self.clear_content()
        self.update_title("📋 إدارة الفواتير")
        self.update_status("عرض الفواتير")
        self.set_active_button("الفواتير")
        InvoicesPage(self.content_frame)

    def show_sales_invoice(self):
        self.clear_content()
        self.update_title("🧾 فاتورة المبيعات")
        self.update_status("إنشاء فاتورة مبيعات")
        self.set_active_button("فاتورة المبيعات")
        # استخدام صفحة فاتورة المبيعات الحقيقية
        SalesInvoicePage(self.content_frame, styles, self)

    def show_purchase_invoice(self):
        self.clear_content()
        self.update_title("📝 فاتورة المشتريات")
        self.update_status("إنشاء فاتورة مشتريات")
        self.set_active_button("فاتورة المشتريات")
        # يمكن إضافة صفحة فاتورة المشتريات هنا
        tk.Label(self.content_frame, text="صفحة فاتورة المشتريات قيد التطوير", 
                bg=self.card_color, fg=self.text_color, font=styles.FONT_MEDIUM).pack(pady=50)

    def show_returns(self):
        self.clear_content()
        self.update_title("↩️ فاتورة الاستخلاص")
        self.update_status("إنشاء فاتورة استخلاص/إرجاع")
        self.set_active_button("فاتورة الاستخلاص")
        # يمكن إضافة صفحة فاتورة الاستخلاص هنا
        tk.Label(self.content_frame, text="صفحة فاتورة الاستخلاص قيد التطوير", 
                bg=self.card_color, fg=self.text_color, font=styles.FONT_MEDIUM).pack(pady=50)

    def show_reports(self):
        self.clear_content()
        self.update_title("📊 التقارير")
        self.update_status("عرض التقارير")
        self.set_active_button("التقارير")
        # يمكن إضافة صفحة التقارير هنا
        tk.Label(self.content_frame, text="صفحة التقارير قيد التطوير", 
                bg=self.card_color, fg=self.text_color, font=styles.FONT_MEDIUM).pack(pady=50)

if __name__ == "__main__":
    database.create_tables()
    root = tk.Tk()
    app = SalesManagementApp(root)
    root.mainloop()
