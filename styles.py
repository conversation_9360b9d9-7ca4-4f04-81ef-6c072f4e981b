# ألوان أساسية محسنة
PRIMARY_COLOR = "#3498db"      # أزرق أنيق
SECONDARY_COLOR = "#2ecc71"    # أخضر جميل
ACCENT_COLOR = "#e74c3c"       # أحمر للتنبيهات
SUCCESS_COLOR = "#27ae60"      # أخضر للنجاح
WARNING_COLOR = "#f39c12"      # برتقالي للتحذيرات

# ألوان الخلفية
BACKGROUND_COLOR = "#f8f9fa"   # رمادي فاتح جداً
LIGHT_COLOR = "#ffffff"        # أبيض نقي
DARK_COLOR = "#2c3e50"         # رمادي داكن أنيق
CARD_COLOR = "#ffffff"         # أبيض للبطاقات
SHADOW_COLOR = "#e0e0e0"       # ظل خفيف

# ألوان النص
TEXT_COLOR = "#2c3e50"         # رمادي داكن للنص الرئيسي
TEXT_LIGHT = "#7f8c8d"         # رمادي فاتح للنص الثانوي
TEXT_WHITE = "#ffffff"        # أبيض للنص على الخلفيات الداكنة

# ألوان الشريط الجانبي
SIDEBAR_BG = "#34495e"         # رمادي داكن للخلفية
SIDEBAR_HOVER = "#3498db"      # أزرق عند التمرير
SIDEBAR_ACTIVE = "#2980b9"     # أزرق داكن للزر النشط
SIDEBAR_TEXT = "#ecf0f1"       # أبيض مائل للرمادي

# تدرجات لونية للتأثيرات
GRADIENT_START = "#3498db"
GRADIENT_END = "#2980b9"

# الخطوط المحسنة
FONT_TITLE = ('Arial', 24, 'bold')      # للعناوين الرئيسية
FONT_LARGE = ('Arial', 18, 'bold')      # للعناوين الكبيرة
FONT_MEDIUM = ('Arial', 14)             # للنص المتوسط
FONT_SMALL = ('Arial', 11)              # للنص الصغير
FONT_TINY = ('Arial', 9)                # للملاحظات
ICON_FONT = ('Arial', 20)               # للأيقونات

# أبعاد وتباعد
PADDING_SMALL = 5
PADDING_MEDIUM = 10
PADDING_LARGE = 20
MARGIN_SMALL = 5
MARGIN_MEDIUM = 10
MARGIN_LARGE = 20

# أنماط الحدود
BORDER_RADIUS = 8
BORDER_WIDTH = 1
SHADOW_OFFSET = 2

# أنماط الأزرار
BUTTON_STYLES = {
    'primary': {
        'bg': PRIMARY_COLOR,
        'fg': TEXT_WHITE,
        'activebackground': "#2980b9",
        'activeforeground': TEXT_WHITE,
        'borderwidth': 0,
        'highlightthickness': 0,
        'relief': 'flat',
        'font': FONT_MEDIUM,
        'padx': 20,
        'pady': 10,
        'cursor': 'hand2'
    },
    'secondary': {
        'bg': SECONDARY_COLOR,
        'fg': TEXT_WHITE,
        'activebackground': "#27ae60",
        'activeforeground': TEXT_WHITE,
        'borderwidth': 0,
        'highlightthickness': 0,
        'relief': 'flat',
        'font': FONT_MEDIUM,
        'padx': 20,
        'pady': 10,
        'cursor': 'hand2'
    },
    'danger': {
        'bg': ACCENT_COLOR,
        'fg': TEXT_WHITE,
        'activebackground': "#c0392b",
        'activeforeground': TEXT_WHITE,
        'borderwidth': 0,
        'highlightthickness': 0,
        'relief': 'flat',
        'font': FONT_MEDIUM,
        'padx': 20,
        'pady': 10,
        'cursor': 'hand2'
    },
    'outline': {
        'bg': LIGHT_COLOR,
        'fg': PRIMARY_COLOR,
        'activebackground': "#f8f9fa",
        'activeforeground': PRIMARY_COLOR,
        'borderwidth': 1,
        'highlightthickness': 0,
        'relief': 'solid',
        'font': FONT_MEDIUM,
        'padx': 20,
        'pady': 10,
        'cursor': 'hand2'
    }
}

# تأثيرات الظلال
ELEVATION_STYLES = {
    'low': {'shadow': (0, 1, 3), 'color': '#00000012'},
    'medium': {'shadow': (0, 1, 5), 'color': '#00000012'},
    'high': {'shadow': (0, 1, 8), 'color': '#00000012'}
}

# تأثيرات التدرجات
GRADIENT_STYLES = {
    'primary': {'from': PRIMARY_COLOR, 'to': "#2980b9"},
    'secondary': {'from': SECONDARY_COLOR, 'to': "#27ae60"},
    'accent': {'from': ACCENT_COLOR, 'to': "#c0392b"}
}